"""
DAG loader test for Signal2Segment DAG.

This test ensures that the DAG can be loaded without import errors,
following Airflow best practices for DAG testing.
"""

import subprocess
import sys
from pathlib import Path


def test_dag_loads_without_errors():
    """Test that DAG loads without import errors."""
    dag_file = Path("dags/signal2segment/signal2segment_dag.py")
    
    # Test DAG file can be imported without errors
    result = subprocess.run([
        sys.executable, "-c", f"import sys; sys.path.append('.'); exec(open('{dag_file}').read())"
    ], capture_output=True, text=True, cwd=".")
    
    if result.returncode != 0:
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
    
    assert result.returncode == 0, f"DAG failed to load: {result.stderr}"


def test_dag_file_syntax():
    """Test that DAG file has valid Python syntax."""
    dag_file = Path("dags/signal2segment/signal2segment_dag.py")
    
    # Test syntax by compiling the file
    result = subprocess.run([
        sys.executable, "-m", "py_compile", str(dag_file)
    ], capture_output=True, text=True)
    
    assert result.returncode == 0, f"DAG has syntax errors: {result.stderr}"


def test_all_task_files_load():
    """Test that all task files can be imported."""
    task_files = [
        "dags/signal2segment/tasks/validation.py",
        "dags/signal2segment/tasks/audience_generation.py",
        "dags/signal2segment/tasks/delivery.py",
    ]
    
    for task_file in task_files:
        result = subprocess.run([
            sys.executable, "-c", f"import sys; sys.path.append('.'); exec(open('{task_file}').read())"
        ], capture_output=True, text=True, cwd=".")
        
        assert result.returncode == 0, f"Task file {task_file} failed to load: {result.stderr}"


def test_config_file_loads():
    """Test that config file can be imported."""
    config_file = "dags/signal2segment/config.py"
    
    result = subprocess.run([
        sys.executable, "-c", f"import sys; sys.path.append('.'); exec(open('{config_file}').read())"
    ], capture_output=True, text=True, cwd=".")
    
    assert result.returncode == 0, f"Config file failed to load: {result.stderr}"
