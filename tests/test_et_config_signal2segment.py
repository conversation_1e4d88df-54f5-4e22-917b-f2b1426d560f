"""
Unit tests for Signal2Segment et_config integration.

These tests validate that the Signal2Segment DAG configurations
are properly integrated into the et_config structure.
"""

import pytest
from unittest.mock import patch, MagicMock


class TestSignal2SegmentETConfig:
    """Test Signal2Segment integration with et_config."""

    def test_signal2segment_config_available_in_all_environments(self):
        """Test that Signal2Segment config is available in all environments."""
        from et_config import get_environment_config

        environments = ["local", "dev", "prod"]

        for env in environments:
            env_config = get_environment_config(env)

            # Test that all required configurations are available
            lambda_config = env_config.get_lambda_config("signal2segment_validation")
            pygene_config = env_config.get_pygene_config("signal2segment")
            perf_config = env_config.get_performance_config("signal2segment")
            query_map = env_config.get_query_map("signal2segment")

            assert lambda_config is not None, f"Lambda config missing in {env}"
            assert pygene_config is not None, f"PyGene config missing in {env}"
            assert perf_config is not None, f"Performance config missing in {env}"
            assert query_map is not None, f"Query map missing in {env}"
            assert len(query_map) > 0

    def test_environment_specific_lambda_configs(self):
        """Test that Lambda configurations are environment-specific."""
        from et_config import get_lambda_configuration

        # Test local environment
        local_config = get_lambda_configuration("local", "signal2segment_validation")
        assert "dev-quoteprospect" in local_config["function_name"]
        assert local_config["validation_limit"] == 1

        # Test dev environment
        dev_config = get_lambda_configuration("dev", "signal2segment_validation")
        assert "dev-quoteprospect" in dev_config["function_name"]
        assert dev_config["validation_limit"] == 3

        # Test prod environment
        prod_config = get_lambda_configuration("prod", "signal2segment_validation")
        assert "prod-quoteprospect" in prod_config["function_name"]
        assert prod_config["validation_limit"] == 3

    def test_environment_specific_performance_configs(self):
        """Test that performance configurations are environment-specific."""
        from et_config import get_performance_configuration

        # Test local environment (reduced parallelism)
        local_config = get_performance_configuration("local", "signal2segment")
        assert local_config["max_active_tis_per_dag"] == 2
        assert local_config["default_retries"] == 1

        # Test production environment (full parallelism)
        prod_config = get_performance_configuration("prod", "signal2segment")
        assert prod_config["max_active_tis_per_dag"] == 10
        assert prod_config["default_retries"] == 3

    @patch("airflow.models.Variable.get")
    def test_pygene_credentials_integration(self, mock_var_get):
        """Test PyGene credentials integration with et_config."""
        import json
        from et_config import get_pygene_credentials

        mock_var_get.return_value = json.dumps(
            {"client_id": "test_client_id", "client_secret": "test_client_secret"}
        )

        # Test production environment
        result = get_pygene_credentials("prod", "signal2segment")

        assert result["client_id"] == "test_client_id"
        assert result["client_secret"] == "test_client_secret"
        mock_var_get.assert_called_with("dataservices_gene_creds_prod")

    def test_notification_configuration_integration(self):
        """Test notification configuration integration using modern pattern."""
        from et_config import get_environment_config

        # Test that we can access notification configuration directly
        env_config = get_environment_config()

        # Test that notification config has expected structure
        assert hasattr(env_config, "notifications")
        assert hasattr(env_config.notifications, "slack_webhook")
        assert hasattr(env_config.notifications, "opsgenie_api_key")
        assert hasattr(env_config.notifications, "email_recipients")

    def test_team_mappings_include_signal2segment(self):
        """Test that team mappings include signal2segment configurations."""
        from et_config.teams.utils import get_team_slack_channel, get_team_emails

        # Test Slack channel mapping
        slack_channel = get_team_slack_channel("signal2segment")
        assert slack_channel == "#signal2segment-alerts"

        # Test email mapping
        emails = get_team_emails("signal2segment")
        assert "<EMAIL>" in emails
        assert "<EMAIL>" in emails

    def test_dag_config_registry_functions(self):
        """Test DAG configuration registry functions."""
        from et_config import (
            get_signal2segment_config,
            validate_dag_configuration,
            list_available_dag_configs,
        )

        # Test get_signal2segment_config
        config = get_signal2segment_config("dev")
        assert config is not None
        assert hasattr(config, "lambda_config")

        # Test validation
        is_valid = validate_dag_configuration("signal2segment", "dev")
        assert is_valid is True

        # Test listing available configs
        available_configs = list_available_dag_configs("dev")
        assert "signal2segment" in available_configs

    def test_query_map_consistency(self):
        """Test that query map is consistent across environments."""
        from et_config import get_signal2segment_config

        environments = ["local", "dev", "prod"]
        query_maps = []

        for env in environments:
            config = get_signal2segment_config(env)
            query_maps.append(config.query_map)

        # All environments should have the same query map
        for query_map in query_maps[1:]:
            assert query_map == query_maps[0]

        # Check that expected audience types are present
        expected_types = [
            "AUDIENCE_TYPE_PROSPECTS_BY_INTENT",
            "AUDIENCE_TYPE_PROSPECTS_LIKELY_HOME_SELLERS",
            "AUDIENCE_TYPE_PROSPECTS_IN_HOUSING_MARKET",
        ]

        for audience_type in expected_types:
            assert audience_type in query_maps[0]

    def test_configuration_model_validation(self):
        """Test that configuration models have proper validation."""
        from et_config.models.dag_configs import (
            LambdaConfig,
            PyGeneConfig,
            PerformanceConfig,
            Signal2SegmentConfig,
        )

        # Test LambdaConfig
        lambda_config = LambdaConfig(function_name="test-function", validation_limit=5)
        assert lambda_config.function_name == "test-function"
        assert lambda_config.validation_limit == 5
        assert lambda_config.region_name == "us-east-1"  # Default value

        # Test PyGeneConfig
        pygene_config = PyGeneConfig(credentials_variable_name="test_creds")
        assert pygene_config.credentials_variable_name == "test_creds"
        assert pygene_config.default_data_source == "airflow"  # Default value

        # Test PerformanceConfig
        perf_config = PerformanceConfig(max_active_tis_per_dag=5, default_retries=2)
        assert perf_config.max_active_tis_per_dag == 5
        assert perf_config.default_retries == 2

        # Test Signal2SegmentConfig
        s2s_config = Signal2SegmentConfig(
            lambda_config=lambda_config,
            pygene_config=pygene_config,
            performance_config=perf_config,
        )
        assert s2s_config.lambda_config == lambda_config
        assert s2s_config.pygene_config == pygene_config
        assert s2s_config.performance_config == perf_config
        assert len(s2s_config.query_map) > 0  # Should have default query map

    def test_environment_config_dag_config_method(self):
        """Test EnvironmentConfig.get_dag_config method."""
        from et_config import get_environment_config

        env_config = get_environment_config("dev")

        # Test getting signal2segment config
        s2s_config = env_config.get_dag_config("signal2segment")
        assert s2s_config is not None

        # Test getting non-existent config
        non_existent = env_config.get_dag_config("non_existent_dag")
        assert non_existent is None

    def test_configuration_factory_functions(self):
        """Test configuration factory functions."""
        from et_config.models.dag_configs import (
            create_local_signal2segment_config,
            create_dev_signal2segment_config,
            create_prod_signal2segment_config,
        )

        # Test local config factory
        local_config = create_local_signal2segment_config()
        assert local_config.performance_config.max_active_tis_per_dag == 2
        assert local_config.lambda_config.validation_limit == 1

        # Test dev config factory
        dev_config = create_dev_signal2segment_config()
        assert dev_config.performance_config.max_active_tis_per_dag == 5
        assert dev_config.lambda_config.validation_limit == 3

        # Test prod config factory
        prod_config = create_prod_signal2segment_config()
        assert prod_config.performance_config.max_active_tis_per_dag == 10
        assert prod_config.lambda_config.validation_limit == 3
