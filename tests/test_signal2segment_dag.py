"""
Unit tests for Signal2Segment DAG.

These tests focus on the core logic and configuration of the DAG without
excessive coupling to Airflow internals. Tests are organized to validate:

1. DAG structure and configuration
2. Task function logic (independent of Airflow)
3. Environment-aware behavior
4. Error handling
"""

import json
import pytest
from unittest.mock import patch, MagicMock, call
from airflow.models import DagBag
from airflow.exceptions import AirflowFailException, AirflowSkipException


class TestSignal2SegmentDAGStructure:
    """Test DAG structure and configuration."""

    @pytest.fixture
    def dagbag(self):
        """Create DagBag for testing."""
        return DagBag(dag_folder="dags/signal2segment/", include_examples=False)

    def test_dag_loads_without_errors(self, dagbag):
        """Test that DAG loads without import errors."""
        assert dagbag.import_errors == {}
        assert "signal2segment_modernized" in dagbag.dag_ids

    def test_dag_configuration(self, dagbag):
        """Test DAG configuration and metadata."""
        dag = dagbag.get_dag("signal2segment_modernized")

        assert dag is not None
        assert dag.owner == "data-engineering"
        assert (
            dag.description
            == "Modernized signal-to-segment audience generation and delivery system"
        )
        assert "audience" in dag.tags
        assert "modernized" in dag.tags
        assert dag.catchup is False
        assert dag.max_active_tasks == 3
        assert dag.max_active_runs == 1

    def test_dag_has_required_tasks(self, dagbag):
        """Test that DAG contains expected tasks."""
        dag = dagbag.get_dag("signal2segment_modernized")
        task_ids = [task.task_id for task in dag.tasks]

        # Should have configuration extraction task
        assert "get_configurations" in task_ids

        # Should have task group for processing
        assert any("process_configurations" in task_id for task_id in task_ids)


class TestConfigurationFunctions:
    """Test configuration functions."""

    @patch("et_config.dag_registry.get_current_environment")
    @patch("airflow.models.Variable.get")
    def test_get_pygene_credentials_prod(self, mock_var_get, mock_env):
        """Test PyGene credentials retrieval in production."""
        from et_config import get_pygene_credentials

        mock_env.return_value = "prod"
        mock_var_get.return_value = json.dumps(
            {"client_id": "prod_client_id", "client_secret": "prod_client_secret"}
        )

        result = get_pygene_credentials()

        assert result["client_id"] == "prod_client_id"
        assert result["client_secret"] == "prod_client_secret"
        mock_var_get.assert_called_once_with("dataservices_gene_creds_prod")

    @patch("dags.signal2segment.config.get_current_environment")
    def test_get_pygene_credentials_local_fallback(self, mock_env):
        """Test PyGene credentials fallback for local environment."""
        from dags.signal2segment.config import get_pygene_credentials

        mock_env.return_value = "local"

        with patch("airflow.models.Variable.get", side_effect=Exception("No variable")):
            result = get_pygene_credentials()

            assert result["client_id"] == "mock_client_id"
            assert result["client_secret"] == "mock_client_secret"

    @patch("dags.signal2segment.config.get_current_environment")
    def test_get_lambda_config_environment_specific(self, mock_env):
        """Test Lambda configuration is environment-specific."""
        from dags.signal2segment.config import get_lambda_config

        # Test local environment
        mock_env.return_value = "local"
        local_config = get_lambda_config()
        assert "dev-quoteprospect" in local_config["function_name"]
        assert local_config["validation_limit"] == 1

        # Test dev environment
        mock_env.return_value = "dev"
        dev_config = get_lambda_config()
        assert "dev-quoteprospect" in dev_config["function_name"]
        assert dev_config["validation_limit"] == 3

        # Test prod environment
        mock_env.return_value = "prod"
        prod_config = get_lambda_config()
        assert "prod-quoteprospect" in prod_config["function_name"]
        assert prod_config["validation_limit"] == 3

    @patch("dags.signal2segment.config.get_current_environment")
    def test_get_dag_config_environment_specific(self, mock_env):
        """Test DAG configuration is environment-specific."""
        from dags.signal2segment.config import get_dag_config

        # Test local environment (reduced parallelism)
        mock_env.return_value = "local"
        local_config = get_dag_config()
        assert local_config["max_active_tis_per_dag"] == 2
        assert local_config["default_retries"] == 1

        # Test production environment (full parallelism)
        mock_env.return_value = "prod"
        prod_config = get_dag_config()
        assert prod_config["max_active_tis_per_dag"] == 10
        assert prod_config["default_retries"] == 3


class TestTaskFunctions:
    """Test individual task functions."""

    def test_get_configurations_with_valid_config(self):
        """Test configuration extraction with valid input."""
        from dags.signal2segment.signal2segment_dag import signal2segment_dag

        # Get the task function
        dag_instance = signal2segment_dag()

        # Mock context with valid configuration
        mock_context = {
            "dag_run": MagicMock(),
        }
        mock_context["dag_run"].conf.get.return_value = [
            {
                "org_id": "test_org",
                "audience_type": "AUDIENCE_TYPE_PROSPECTS_BY_INTENT",
                "query_args": "test_args",
                "data_source": "test_source",
                "audience_name_prefix": "test_audience",
                "automation": {
                    "interval_value": 1,
                    "interval_unit": "DAYS",
                    "type": "REFRESH",
                },
                "destinations": [
                    {
                        "order_line_id": "test_ol_id",
                        "is_exclude": False,
                        "detach_previous_audience": True,
                    }
                ],
            }
        ]

        # This would require more complex mocking to test the actual task execution
        # For now, we test that the configuration structure is correct
        expected_config = mock_context["dag_run"].conf.get.return_value
        assert len(expected_config) == 1
        assert expected_config[0]["org_id"] == "test_org"

    def test_get_configurations_empty_config(self):
        """Test configuration extraction with empty input."""
        # This would test the AirflowSkipException case
        # Implementation would require mocking the task execution context
        pass


class TestValidationTask:
    """Test validation task logic."""

    @patch("dags.signal2segment.tasks.validation.get_lambda_config")
    @patch(
        "airflow.providers.amazon.aws.operators.lambda_function.LambdaInvokeFunctionOperator"
    )
    def test_validate_configuration_success(self, mock_lambda_op, mock_lambda_config):
        """Test successful configuration validation."""
        from dags.signal2segment.tasks.validation import validate_configuration

        # Mock Lambda configuration
        mock_lambda_config.return_value = {
            "function_name": "test-function",
            "validation_limit": 3,
            "invocation_type": "RequestResponse",
            "log_type": "Tail",
            "region_name": "us-east-1",
        }

        # Mock Lambda response
        mock_lambda_instance = MagicMock()
        mock_lambda_instance.execute.return_value = json.dumps(
            {"status": "success", "count": 100}
        )
        mock_lambda_op.return_value = mock_lambda_instance

        # Test configuration
        test_config = {
            "org_id": "test_org",
            "audience_type": "AUDIENCE_TYPE_PROSPECTS_BY_INTENT",
            "query_args": "test_args",
            "data_source": "test_source",
            "audience_name_prefix": "test_audience",
            "destinations": [{"order_line_id": "test_ol"}],
        }

        # Execute validation
        result = validate_configuration.python_callable(test_config)

        # Verify result
        assert result["org_id"] == "test_org"
        assert "validation_metadata" in result
        assert result["validation_metadata"]["query_id"] is not None

    def test_validate_configuration_missing_fields(self):
        """Test validation with missing required fields."""
        from dags.signal2segment.tasks.validation import validate_configuration

        # Test configuration missing required fields
        test_config = {
            "org_id": "test_org",
            # Missing other required fields
        }

        # Should raise AirflowFailException
        with pytest.raises(AirflowFailException):
            validate_configuration.python_callable(test_config)

    def test_validate_configuration_invalid_audience_type(self):
        """Test validation with invalid audience type."""
        from dags.signal2segment.tasks.validation import validate_configuration

        # Test configuration with invalid audience type
        test_config = {
            "org_id": "test_org",
            "audience_type": "INVALID_AUDIENCE_TYPE",
            "query_args": "test_args",
            "data_source": "test_source",
            "audience_name_prefix": "test_audience",
            "destinations": [{"order_line_id": "test_ol"}],
        }

        # Should raise AirflowFailException
        with pytest.raises(AirflowFailException):
            validate_configuration.python_callable(test_config)


class TestEnvironmentAwareBehavior:
    """Test environment-aware behavior."""

    @patch("dags.signal2segment.config.get_current_environment")
    def test_notification_config_environment_aware(self, mock_env):
        """Test that notification configuration adapts to environment."""
        from dags.signal2segment.config import _get_notification_config

        notification_config = _get_notification_config()

        # Test that it returns a NotificationConfig instance
        assert hasattr(notification_config, "slack_on_failure")
        assert notification_config.slack_on_failure is True
        assert notification_config.team_owner == "data-engineering"
        assert notification_config.slack_channel_override == "#signal2segment-alerts"


# Integration test placeholder
class TestDAGIntegration:
    """Integration tests for the complete DAG."""

    def test_dag_can_be_instantiated(self):
        """Test that DAG can be instantiated without errors."""
        try:
            from dags.signal2segment.signal2segment_dag import dag

            assert dag is not None
            assert dag.dag_id == "signal2segment_modernized"
        except Exception as e:
            pytest.fail(f"DAG instantiation failed: {e}")

    def test_dag_has_etdag_metadata(self):
        """Test that DAG has ETDAG metadata."""
        from dags.signal2segment.signal2segment_dag import dag

        # Check if ETDAG metadata is present
        if hasattr(dag, "etdag_metadata"):
            metadata = dag.etdag_metadata
            assert metadata["owner"] == "data-engineering"
            assert "data_sources" in metadata
            assert "downstream_systems" in metadata
