from airflow.decorators import dag, task
from airflow.models import Variable
from airflow.operators.python import ShortCircuitOperator
from airflow.providers.eltoro.transfers.s3tos3 import S3ToS3Operator
from airflow.providers.slack.operators.slack import SlackAPIPostOperator
from airflow.utils.dates import days_ago
from old_dags.slack_alert import task_slack_alert
from datetime import timedelta

default_args = {
    "owner": "<PERSON> Carver",
    "email": ["<EMAIL>"],
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}


def changes_found(task_instance):
    results = task_instance.xcom_pull(task_ids="medicx_sync")
    print(results)
    if len(results) > 0:
        return True
    return False


@dag(
    dag_id="medicx_claimstream_sync",
    description="Syncs Medicx Claimstream files between S3 buckets",
    start_date=days_ago(2),
    on_failure_callback=task_slack_alert,
    default_args=default_args,
    schedule_interval="@hourly",
    catchup=False,
    tags=["medicx"],
)
def medicx_claimstream_etl():
    """
    This DAG is responsible for syncing Medicx Claimstream file to S3
    """
    local_s3_conn = "s3_conn"
    medicx_s3_conn = "medicx_s3"
    report_slacker_conn_id = "bi-report-slacker-token"
    env = Variable.get("environment")
    channel_name = f"#medicx-notifications-{env}"

    @task
    def build_slack_message(input_files):
        slack_message = [f"- {filename}" for filename in input_files]
        slack_message.insert(
            0,
            "The following files have been synced from Medicx to s3://eltoro-data-sources/medicx/sync/",
        )
        return "\n".join(slack_message)

    medicx_sync = S3ToS3Operator(
        task_id="medicx_sync",
        source_aws_conn_id=medicx_s3_conn,
        source_s3_bucket="claimsteam-mdx-eltoro-transfer-files",
        source_s3_prefix="",
        dest_aws_conn_id=local_s3_conn,
        dest_s3_bucket="eltoro-data-sources",
        dest_s3_prefix="medicx/sync/",
        replace_dest=False,
        sync_locations=True,
    )

    found_changes = ShortCircuitOperator(
        task_id="found_changes",
        python_callable=changes_found,
    )

    slack_message = build_slack_message(medicx_sync.output)

    slack_alert = SlackAPIPostOperator(
        task_id="slack_alert",
        username="Medicx File Sync",
        slack_conn_id=report_slacker_conn_id,
        text=slack_message,
        channel=channel_name,
    )

    medicx_sync >> found_changes >> slack_message >> slack_alert


medicx_claimstreat_sync_dag = medicx_claimstream_etl()
