from typing import TypedDict
from airflow.models import Variable

class S3Loc(TypedDict):
    s3_bucket: str  # vr-timestamp
    s3_key: str  # bi_sources/toyota_dashboard_datasets/auto_bucklocs.csv

class Job(TypedDict):
    trino_query: str  # "SELECT * FROM s3.gold_auto_intender.locations_new_and_used",
    s3_location: S3Loc
    chunk: bool  # False
    gzip_compress: bool # False


env_var = Variable.get("environment", "dev") #default value dev
if env_var == "prod":
    s3_key = "bi_sources/toyota_dashboard_datasets/toyota_stats_dashboard/toyota_dashboard_stats.csv"
else:
    s3_key = "bi_sources/toyota_dashboard_datasets/dev/toyota_stats_dashboard/toyota_dashboard_stats.csv"

job = Job(
        trino_query="SELECT * FROM olympus.dev_prototyping.toyota_dashboard_stats;",
        s3_location=S3Loc(
            s3_bucket="vr-timestamp", 
            s3_key=f"{s3_key}"
        ),
        chunk=False,
        gzip_compress=False
    )

merge_query = '''
MERGE INTO olympus.dev_prototyping.toyota_dashboard_stats AS target
USING (
    SELECT
        aid.event_date AS event_date_for_merge, -- Renamed to avoid confusion with target.date
        (SELECT COUNT(DISTINCT ethash_v1) FROM s3.bronze_auto_intender.auto_intent_daily_4eyes WHERE event_date = aid.event_date) AS daily_auto_intent_ethash,
        (SELECT COUNT(DISTINCT buckloc_id) FROM s3.gold_auto_intender.location_tags) AS distinct_location_tags,
        (SELECT COUNT(DISTINCT buckloc_id) FROM s3.gold_auto_intender.distinct_obs_90 WHERE date = aid.event_date) AS daily_distinct_locs_seen,
        (SELECT COUNT(DISTINCT ROW(location, deviceid)) FROM s3.bronze_ts_reports.observations WHERE CONTAINS(ARRAY['auto_dealers', 'used_cars'], bucket) AND date = aid.event_date) AS daily_distinct_raw_obs,
        (SELECT COUNT(DISTINCT visitor_ethash) FROM s3.gold_auto_intender.distinct_obs_90 WHERE date = aid.event_date) AS daily_distinct_obs_ethash,
        (SELECT COUNT(DISTINCT parcel_ethash) FROM s3.gold_auto_intender.base WHERE date = aid.event_date) AS daily_base_parcel_ethash
    FROM s3.bronze_auto_intender.auto_intent_daily_4eyes aid
    WHERE aid.event_date >= CURRENT_DATE - INTERVAL '14' DAY
    GROUP BY aid.event_date
) AS source
ON target.date = source.event_date_for_merge -- Corrected to target.date
WHEN MATCHED THEN
    UPDATE SET
        daily_auto_intent_ethash = source.daily_auto_intent_ethash,
        distinct_location_tags = source.distinct_location_tags,
        daily_distinct_locs_seen = source.daily_distinct_locs_seen,
        daily_distinct_raw_obs = source.daily_distinct_raw_obs,
        daily_distinct_obs_ethash = source.daily_distinct_obs_ethash,
        daily_base_parcel_ethash = source.daily_base_parcel_ethash
WHEN NOT MATCHED THEN
    INSERT (date, daily_auto_intent_ethash, distinct_location_tags, daily_distinct_locs_seen, daily_distinct_raw_obs, daily_distinct_obs_ethash, daily_base_parcel_ethash) -- Corrected to date
    VALUES (source.event_date_for_merge, source.daily_auto_intent_ethash, source.distinct_location_tags, source.daily_distinct_locs_seen, source.daily_distinct_raw_obs, source.daily_distinct_obs_ethash, source.daily_base_parcel_ethash);
'''