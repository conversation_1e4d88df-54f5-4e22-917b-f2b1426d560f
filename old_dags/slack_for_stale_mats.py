from datetime import datetime, timedelta
from airflow.decorators import task
from etdag import ETDAG
from airflow.providers.slack.operators.slack_webhook import SlackWebhookOperator
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.exceptions import AirflowSkipException

"""
    DAG: ``
    
    Schedule: once an hour EDT daily
    
    Owner:  <PERSON> Price
    
    ## Summary
    Queries starburst for stale materialized views and sends a slack message to the starburst
    channel so the owner can refresh it
    
    ## Time Dependent
    Dag runs are not time dependent
    
    ## Failures
    In the event of a failure this Dag can be rerun manually by clearing the task.
    
    ## Escalation
    If rerunning the Dag <NAME_EMAIL>.
    
    ## Dependencies
    none
    
    ## Results
    Should find stale views and send an alert to the starburst channel
    

"""


default_args = {"owner": "bp", "retries": 3, "retry_delay": timedelta(minutes=1)}

FIND_STALE_MAT_VIEWS = """
SELECT catalog_name, schema_name, name from "system"."metadata"."materialized_views" 
where catalog_name = 's3' and freshness != 'FRESH' 
"""


@task
def parse_results(output):
    if output:
        results = output
        message = "🚨 *Stale Materialized Views Found* 🚨\n"
        for row in results:
            catalog_name, schema_name, name = row
            print(schema_name, name)
            message += f'• "{catalog_name}"."{schema_name}"."{name}"\n'
    else:
        raise AirflowSkipException("Zero stale views to report")
    return message


with ETDAG(
    dag_id="slack_alert_for_stale_mats",
    schedule="0 */2 * * *",
    default_args=default_args,
    catchup=False,
    description="will send a slack message whn it finds stale mat views",
    start_date=datetime(2024, 9, 27),
    tags=["stale_mats", "starburst"],
    et_failure_msg=False,
) as dag:

    stale_mats = SQLExecuteQueryOperator(
        task_id="find_stale_mats",
        conn_id="trino_conn",
        sql=FIND_STALE_MAT_VIEWS,
        handler=lambda result: result.fetchall(),
    )

    results = parse_results(stale_mats.output)

    send_slack_message = SlackWebhookOperator(
        task_id="send_slack_message",
        slack_webhook_conn_id="stale-materialized-views",
        message=results,
        channel="starburst",
        username="Materialized View Checker",
        icon_emoji=":shaking-fist-dark-mode:",
    )
stale_mats >> results >> send_slack_message
