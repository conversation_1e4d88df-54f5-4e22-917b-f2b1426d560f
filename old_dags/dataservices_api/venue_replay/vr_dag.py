from airflow.decorators import task
from etdag import ETDAG
from datetime import datetime, timedelta
from airflow.models import Variable
from old_dags.dataservices_api.onspot_task_group import create_onspot_request_task_group
from old_dags.dataservices_api.venue_replay.process_data import (
    sync_addresstodevice,
    sync_observations,
    aggregate_and_upload,
    generate_vr_audience,
    poll_for_audience_status,
)
from airflow.exceptions import AirflowFailException, AirflowException

import logging

logger = logging.getLogger(__name__)

docs = """
# Venue Replay Job Workflow DAG

## Overview
This DAG is responsible for executing the **Venue Replay job workflow**, which processes geolocation data 
by making external API requests, monitoring responses, and generating structured outputs in CSV and Parquet formats. 

## Execution & Triggering
- This DAG **must only be triggered by the `Target Job service`**.
- It does **not** run on a schedule and must be triggered manually via an API request.
- The DAG is **idempotent** and supports retries at almost any stage without causing inconsistencies.
- The Generate Audience task must only be RE-TRIGGERED after looking into the error message.
- Multiple re-triggers of the Generate Audience task could result in extra dag runs.

## Concurrency & Parallelism
- The DAG allows **up to 3 concurrent runs**, ensuring controlled execution while supporting multiple requests.
- Tasks are structured to leverage **parallel execution** where applicable, such as request partitioning and response processing.

## Stages of Execution
1. **Prepare Input:** Formats and uploads request data to S3.
2. **Send Requests & Monitor Responses:** Sends OnSpot API requests and waits for results.
3. **Process Responses:** Aggregates response data, performs necessary transformations, and updates the job status.
4. **Uploads Processed Response:** Converts processed data into CSV format and uploads it to S3.
5. **Generate Audience:** Generates audience in NextGen Audience API.

## Retry & Failure Handling
- Each stage is **safe to retry** independently, allowing partial reprocessing without impacting overall data integrity.
- Before re-triggering the Generate Audience task, look into the error message.
- The DAG **does not enforce failure propagation** between tasks—ensuring isolated retries if required.

"""


# Parses inputs passed from the Target Job service and creates the requestor output
@task(retries=3, retry_delay=timedelta(seconds=15))
def requestor_output_creator(**context):
    passed_values = context["dag_run"].conf or {}
    input_s3_uri = passed_values.get("input_s3_uri")
    output_s3_uri = passed_values.get("output_s3_uri")
    audience_id = passed_values.get("audience_id")
    org_id = passed_values.get("org_id")

    endpoint_configs = {
        "observations": {
            "endpoint": "/save/geoframe/all/observations",
            "request_type": "observations",
            "latlng_prefix": "obs",
        },
        "addresstodevice": {
            "endpoint": "/save/geoframe/household/devicesbyaddress",
            "request_type": "addresstodevice",
            "latlng_prefix": "hh",
        },
    }
    endpoint_list = [config["endpoint"] for config in endpoint_configs.values()]

    if not input_s3_uri or not output_s3_uri or not audience_id:
        error_message = f"Missing required parameters! "
        logger.error(error_message)
        context["ti"].xcom_push(key="error_message", value=error_message)
        raise AirflowFailException(
            f"Missing required parameters! "
            f"Received: input_s3_uri={input_s3_uri}, output_s3_uri={output_s3_uri}, audience_id={audience_id}"
        )

    return {
        "request_id": audience_id,
        "org_id": org_id,
        "prepared_features_s3_uri": input_s3_uri,
        "output_s3_uri": output_s3_uri,
        "endpoint_list": endpoint_list,
        "secret_name": "prod/bi/onspot-vr-api",
    }


# Syncs the partition metadata for the AddressToDevice table in Trino
@task(retries=3, retry_delay=timedelta(seconds=15))
def sync_partitions_atd(requestor_output, **kwargs):
    try:
        print(requestor_output)
        audience_id = requestor_output["request_id"]
        env = Variable.get("environment")
        sync_addresstodevice(audience_id=audience_id, env=env)
    except Exception as e:
        error_message = f"Sync atd failed for audience_id: {audience_id}"
        logger.error(error_message)
        kwargs["ti"].xcom_push(key="error_message", value=error_message)
        raise AirflowException(f"Sync failed: {e}")


# Syncs the partition metadata for the Observations table in Trino
@task(retries=3, retry_delay=timedelta(seconds=15))
def sync_partitions_obs(requestor_output, **kwargs):
    try:
        audience_id = requestor_output["request_id"]
        env = Variable.get("environment")
        sync_observations(audience_id=audience_id, env=env)
    except Exception as e:
        error_message = f"Sync obs failed for audience_id: {audience_id}"
        logger.error(error_message)
        kwargs["ti"].xcom_push(key="error_message", value=error_message)
        raise AirflowException(f"Sync failed: {e}")


# Aggregates the data and uploads it to S3
@task(retries=3, retry_delay=timedelta(seconds=15))
def process_data(requestor_output, **kwargs):
    try:
        env = Variable.get("environment")
        aggregate_and_upload(requestor_output, env=env)
    except Exception as e:
        error_message = (
            f"Process data failed for audience_id: {requestor_output['request_id']}"
        )
        logger.error(error_message)
        kwargs["ti"].xcom_push(key="error_message", value=error_message)
        raise AirflowException(f"Aggregation failed: {e}")


# Generates the audience in NextGen Audience API
@task()
def generate_audience(requestor_output, error: str = None):
    env = Variable.get("environment")
    generate_vr_audience(requestor_output, env=env, error=error)


# Polls for the status of the audience
@task()
def check_audience_status(requestor_output):
    env = Variable.get("environment")
    poll_for_audience_status(requestor_output, env=env)


# Handles failure at any stage of the workflow and passes that value to generate_audience task
@task(trigger_rule="all_done")  # Runs on success and failure
def handle_failure(**kwargs):
    ti = kwargs["ti"]

    failed_tasks = [
        "requestor_output_creator",
        "sync_partitions_atd",
        "sync_partitions_obs",
        "process_data",
        "send_and_monitor_requests.check_onspot_for_errors",
    ]

    found_error = False

    for task_id in failed_tasks:
        error_msg = ti.xcom_pull(task_ids=task_id, key="error_message")
        if error_msg:
            logger.error(f"Handling failure: {task_id} failed with error: {error_msg}")
            ti.xcom_push(key="error_message", value=error_msg)
            found_error = True
            # return error_msg
            return None

    logger.info("No failures detected, pushing None to XCom.")
    ti.xcom_push(key="error_message", value=None)
    return None
default_args = {
    "owner": "BP",
    "retries": 3,
    "retry_delay": timedelta(seconds=30),
}

with ETDAG(
    dag_id="prod_venue_replay_job_workflow",
    catchup=False,
    default_args=default_args,
    is_paused_upon_creation=True,
    start_date=datetime(2025, 1, 22),
    schedule_interval=None,
    max_active_runs=5,
    tags=["venue_replay, DND", "prod"],
    et_failure_msg=True,  # Keep Slack alerts
    et_operations_alert=True,  # JSM Operations alerts (prod only, handled by ETDAG)
    et_operations_priority="P2",  # High priority for venue replay
    params={
        "audience_id": "",
        "org_id": "",
        "input_s3_uri": "",
        "output_s3_uri": "",
    },
) as dag:
    generate_requestor_output = requestor_output_creator()

    onspot_request_task_group_output = create_onspot_request_task_group(
        request_id="{{ dag_run.conf.get('audience_id', 'audience_id') }}",
        requestor_output=generate_requestor_output,
    )

    sync_addresstodevice_task = sync_partitions_atd(generate_requestor_output)
    sync_observations_task = sync_partitions_obs(generate_requestor_output)
    aggregate_and_upload_task = process_data(generate_requestor_output)
    handle_failure_task = handle_failure()
    generate_vr_audience_task = generate_audience(
        generate_requestor_output, handle_failure_task
    )
    poll_for_audience_status_task = check_audience_status(generate_requestor_output)

    (
        generate_requestor_output
        >> onspot_request_task_group_output
        >> [sync_addresstodevice_task, sync_observations_task]
        >> aggregate_and_upload_task
        >> generate_vr_audience_task
        >> poll_for_audience_status_task
    )

    # (
    #     [
    #         generate_requestor_output,
    #         aggregate_and_upload_task,
    #         sync_addresstodevice_task,
    #         sync_observations_task,
    #     ]
    #     >> handle_failure_task
    # )

    # handle_failure_task >> generate_vr_audience_task
