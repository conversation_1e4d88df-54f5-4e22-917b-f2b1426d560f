from etdag import ETDAG
from datetime import datetime, timedelta
from operators.timestamp_report_operator import TimestampReportOperator
#from airflow.operators.dummy_operator import DummyOperator
from airflow.decorators import task
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
import logging
from old_dags.auto_obs_harvester.auto_obs_dep_grps import dep_views, health_check_queries
from airflow.exceptions import AirflowFailException
from airflow.providers.trino.hooks.trino import TrinoHook

logging.basicConfig()
logger = logging.getLogger(__name__)

default_args = {
    "owner": "<PERSON>",
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "retry_exponential_backoff": True,
    "max_retry_delay": timedelta(minutes=20),
}
auto_buckets = ["used_cars", "auto_dealers"]

with ETDAG(
    dag_id="auto_obs_harvester",
    catchup=False,
    default_args=default_args,
    is_paused_upon_creation=True,
    schedule_interval="0 22 * * *",  # Fires every day at 10 PM
    start_date=datetime(2025, 6, 3),
    max_active_runs=1,
    tags=["observations", "auto", "sync_partitions"],
) as dag:

    tables = [
        ("s3", "bronze_ts_reports", "addresstodevice"), #addresstodevice, observations
        ("s3", "bronze_ts_reports", "observations"),
    ]

    # Task 1: Create timestamp reports (no waiting, no retries to prevent costly duplicates)
    auto_ts_create = TimestampReportOperator.partial(
        task_id="create_auto_reports",
        operation="create",
        start="{{ (execution_date - macros.timedelta(days=7)).strftime('%Y-%m-%d') }}",
        end="{{ (execution_date - macros.timedelta(days=4)).strftime('%Y-%m-%d') }}",
        request_types=[
            TimestampReportOperator.REQ_TYPE_ADDRESS_TO_DEVICE,
            TimestampReportOperator.REQ_TYPE_OBSERVATIONS,
        ],
        reduce_to_date=True,
        wait_for_job_to_finish=False,  # KEY CHANGE: Don't wait here
        max_active_tis_per_dag=2,
        retries=0,  # No retries - we don't want to create multiple reports
    ).expand(bucket_id=auto_buckets)

    # Extract job IDs from creation output
    extracted_job_ids_from_create = auto_ts_create.output.map(
        lambda report_output_dict: report_output_dict.get("id")
    )

    # Task 2: Wait for timestamp reports to complete (safe to retry)
    wait_for_completion = TimestampReportOperator.partial(
        task_id="wait_for_auto_reports",
        operation="get",
        wait_for_job_to_finish=True,
        waiter_delay=60,
        waiter_max_attempts=360,  # 6 hours max wait
        retries=5,  # High retries for waiting/polling - this is safe and cheap
        retry_delay=timedelta(minutes=3),
    ).expand(job_id=extracted_job_ids_from_create)

    # Extract job IDs from waiting task output (should be the same IDs)
    extracted_job_ids = wait_for_completion.output.map(
        lambda report_output_dict: report_output_dict.get("id")
    )

    #Performs health checks on each generated report to ensure critical counts are above zero
    ts_health_check = TimestampReportOperator.partial(
        task_id="ts_health_check",
        operation="get",
    ).expand(job_id = extracted_job_ids)

    @task
    def validate_report_health(health_check_output: list):
        location_count = health_check_output.get("locationCount")
        maid_count = health_check_output.get("maidCount")
        id = health_check_output.get("id")
        
        if location_count == 0 or maid_count == 0:
            raise AirflowFailException(f"Health check validation failed: locationCount or maidCount is zero. jobID: {id}, locationCount: {location_count}, maidCount: {maid_count}")

    validated_report = validate_report_health.expand(health_check_output=ts_health_check.output)


    #Synchronizes Starburst partitions for relevant tables
    sync_tasks = SQLExecuteQueryOperator.partial(
        task_id=f"update_ts_partitions",
        conn_id="starburst",
        retries=4,  # Higher retries for database operations
        retry_delay=timedelta(minutes=3),
    ).expand(
        sql=[f"CALL {catalog}.system.sync_partition_metadata('{schema}', '{table}', 'ADD')" for catalog, schema, table in tables]
    )


    #Refreshes dependent materialized views in groups
    refresh_task = None
    for i, view_group in enumerate(dep_views):
        refresh_task = SQLExecuteQueryOperator.partial(
            task_id=f"refresh_group_{i}",
            conn_id="starburst",
            retries=4,  # Higher retries for materialized view refresh
            retry_delay=timedelta(minutes=8),  # Longer delay for resource conflicts
        ).expand(
            sql=[f"REFRESH MATERIALIZED VIEW {view}" for view in view_group]
        )
        if i == 0:
            sync_tasks >> refresh_task
            prev_task = refresh_task
        else:
            prev_task >> refresh_task
            prev_task = refresh_task
        

    #Runs additional health check queries to ensure materialized views have counts above zero for end date in timestamp
    @task(
        retries=3,  # Moderate retries for health checks
        retry_delay=timedelta(minutes=2),
    )
    def run_health_check(query: str):
        hook = TrinoHook(trino_conn_id="starburst")  # Use your connection ID
        result = hook.get_records(query)
        if result[0][0] == 0: #unnest query result
            raise AirflowFailException(f"Health check query failed: {query}. Returned 0.")
        logger.info(f"Query: {query}. Result: {result[0][0]}")

    health_check = run_health_check.expand(
        query=health_check_queries
    )
    
    
    #Chain tasks together
    auto_ts_create >> wait_for_completion >> ts_health_check >> validated_report >> sync_tasks
    refresh_task >> health_check
    
    