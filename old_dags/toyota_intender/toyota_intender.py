from airflow.decorators import task, task_group
from airflow.providers.amazon.aws.transfers.s3_to_sftp import S3ToSFTPOperator
from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.utils.dates import days_ago
from etdag import ETDAG
import numpy as np
from datetime import datetime, timedelta
from old_dags.toyota_intender.query import (
    audience_query,
    new_quote_query,
    deliverable_query,
    metrics_insert_query,
)
from old_dags.toyota_intender.config import segment_rules
from old_dags.toyota_intender.tables import (
    CREATE_PREQUOTE_AUDIENCE_TABLE,
    CREATE_ANNOTATED_TABLE,
    CREATE_TABLE_DELIVERABLE,
)
from airflow.utils.task_group import TaskGroup
from old_dags.quote_file_group import quote_file
from airflow.models import Variable


docs = """
DAG Name: toyota_intender_new

This DAG extracts Toyota intender data from Trino, applies segmentation logic,
prepares quote files, and ultimately delivers a final audience file to SFTP.

Key Features:
1. Environment-Aware Logic:
   - Dynamically assigns table names, S3 paths, and SFTP connections based on
     the Airflow variable 'environment' (e.g., dev vs. prod).
   - Conditionally applies a LIMIT clause if in 'dev' mode for test runs.

2. Table Creation:
   - Three “CREATE TABLE” statements run in parallel under a TaskGroup
     (prequote, annotated, and deliverable).
   - Ensures the required external tables exist before processing.

3. Audience Staging & Segmentation:
   - Pulls data from multiple Trino sources (intender flags, credit flags, etc.).
   - Applies fallback logic to fill missing credit flags.
   - Leverages predefined segment rules to label each row (segment_group, seg_id).
   - Saves final staged data to an S3 Parquet file.

4. Quoting & Annotating:
   - Prepares quote files for new or previously unquoted records, chunking as necessary.
   - Syncs partition metadata so newly added data is discoverable in Trino.

5. Deliverable Generation & Transfer:
   - Gathers final fields (address, state, credit attributes, etc.) into a
     consolidated CSV.
   - Syncs final deliverable partition metadata.
   - Transfers the CSV to an SFTP server, with path and connection determined
     by environment.

6. Retry & Failure Handling:
   - Enhanced retry configuration with 3 retries at DAG level and exponential backoff.
   - Task-specific retry settings:
     * Data processing tasks (stage_*): 4 retries with 3-5 minute delays
     * Database operations (sync_*): 4 retries with 3 minute delays
     * SFTP transfer: 5 retries with 8 minute delays (critical delivery)
   - It is generally safe to rerun or clear a single failed task (e.g., the SFTP
     delivery step) if the upstream data is static or still available on S3.
   - This DAG is not specifically designed to retry sending “yesterday’s” file
     if a failure is unattended for more than 1 day. For that scenario, consider
     re-triggering the DAG for the missed date or incorporating backfill logic.

Schedule:
- Runs daily at 5 AM UTC (`schedule_interval="0 5 * * *"`).

Dependencies:
- The “create_tables” TaskGroup completes first (in parallel).
- Downstream tasks proceed stepwise (stage_prequoted_audience → partition sync →
  quote file prep → final sync → SFTP transfer).
"""

def create_toyota_intender_dag(name, region_list, requote_days, start_hour):
    dag_id = f"toyota_intender_{name}_{requote_days}"

    with ETDAG(
        dag_id=dag_id,
        description="Queries dataset from Trino, parses out the different segments, quotes and delivers to groupg's sftp",
        start_date=days_ago(2),
        default_args={
            "owner": "Rorie Lizenby",
            "depends_on_past": False,
            "email_on_retry": False,
            "retries": 3,  # Add retries for critical DAG
            "retry_delay": timedelta(minutes=5),  # Initial delay
            "retry_exponential_backoff": True,  # Handle external dependencies
            "max_retry_delay": timedelta(minutes=20),  # Cap maximum delay
            "max_active_runs": 1,
        },
        schedule_interval=f"0 {start_hour} * * *",
        catchup=False,
        tags=['app:toyota_intender', 'team:DND'],
        et_failure_msg=True,  # Keep Slack alerts
        et_operations_alert=True,  # JSM Operations alerts (prod only, handled by ETDAG)
        et_operations_priority="P1",  # Critical priority for Toyota
    ) as dag:
        dag.doc_md = docs
        ENV = Variable.get("environment")
        DATE = datetime.today().strftime("%Y-%m-%d")
        YESTERDAY = (datetime.today() - timedelta(days=1)).strftime("%Y-%m-%d")
        ORG_ID = "7sJeuiTWBuCoMMHPq"
        S3_BUCKET = "vr-timestamp"
        S3_PREFIX = f"bi_sources/toyota_2024/{ENV}/"
        SCHEMA = "bronze_auto_intender"
        PREQUOTE_TABLE_NAME = f"{ENV}_toyota_prequote"
        PREQUOTE_AUDIENCE_S3_URI = f"s3://{S3_BUCKET}/{S3_PREFIX}prequote/date={DATE}/group={name}/prequote.parquet"
        STAGED_TARGETS_S3_URI = f"s3://{S3_BUCKET}/{S3_PREFIX}staged_targets/date={DATE}/group={name}/staged_targets.csv"
        ANNOTATED_TABLE_NAME = f"{ENV}_toyota_quote_annotated"
        ANNOTATED_AUDIENCE_S3_URI = f"s3://{S3_BUCKET}/{S3_PREFIX}annotated_audience/date={DATE}/group={name}/annotated_audience.csv"
        SELECTED_AUDIENCE_S3_URI = f"s3://{S3_BUCKET}/{S3_PREFIX}selected_audience/date={DATE}/group={name}/selected_audience.csv"
        DELIVERABLE_TABLE_NAME = f"{ENV}_toyota_deliverable"
        DELIVERABLE_S3_URI = f"s3://{S3_BUCKET}/{S3_PREFIX}deliverable/date={DATE}/group={name}/deliverable.csv"
        METRICS_S3_URI = (f"s3://{S3_BUCKET}/{S3_PREFIX}toyota_metrics/group={name}/metrics.csv")
        METRICS_TABLE_NAME = f"{ENV}_toyota_deliverable_metrics"

        limit_clause = "LIMIT 10000" if ENV == "dev" else ""

        if ENV == "dev":
            SFTP_CONN_ID = "eltoro_sftp"
            SFTP_PATH = f"/incoming/eltoro_toyota/et-daily-{name}-{DATE}.csv"
        else:
            SFTP_CONN_ID = "groupg-reporting"
            SFTP_PATH = f"/et-drop/et-daily-{name}-{DATE}.csv"

        @task(
            retries=4,  # Higher retries for data processing
            retry_delay=timedelta(minutes=3),  # Shorter delay for data tasks
        )
        def stage_prequoted_audience():
            tr = TrinoHook(trino_conn_id="trino_conn")
            df = tr.get_pandas_df(sql=audience_query.format(limit_clause=limit_clause, region_list=region_list))
            print(len(df))
            df.fillna(0, inplace=True)

            # Update rows where both 'NC' and 'GC' are 0 to set 'NC' to 1
            df.loc[(df['NC'] == 0) & (df['GC'] == 0), 'NC'] = 1

            # Create new columns for segment_id, segment_group, and seg_id
            df['segment_group'] = None
            df['seg_id'] = None

            # Iterate over the segment rules in the defined order
            for segment_group, rule in segment_rules.items():
                conditions = rule["conditions"]

                # Apply each condition within the current segment group in list order
                for condition in conditions:
                    seg_id = condition["seg_id"]
                    logic = condition["logic"]

                    # Create a mask for the current condition
                    mask = np.ones(df.shape[0], dtype=bool)
                    for col, val in logic.items():
                        mask &= (df[col] == val)

                    # Assign segment_id and segment_group where the mask is True and no segment has been assigned yet
                    df.loc[mask & df['seg_id'].isna(), 'segment_group'] = segment_group
                    df.loc[mask & df['seg_id'].isna(), 'seg_id'] = seg_id

            columns_to_convert = ['PTI', 'PUI', 'POI', 'TG', 'VTI', 'VOI', 'GC', 'NC']
            df[columns_to_convert] = df[columns_to_convert].astype(int)
            df = df[df['segment_group'].notna()]
            df.to_parquet(PREQUOTE_AUDIENCE_S3_URI, index=False)

        @task(
            retries=4,  # Higher retries for database queries
            retry_delay=timedelta(minutes=5),  # Moderate delay for DB operations
        )
        def stage_quote_file():
            tr = TrinoHook(trino_conn_id="trino_conn")
            df = tr.get_pandas_df(
                sql=new_quote_query.format(
                    SCHEMA=SCHEMA,
                    PREQUOTE_TABLE_NAME=PREQUOTE_TABLE_NAME,
                    ANNOTATED_TABLE_NAME=ANNOTATED_TABLE_NAME,
                    requote_days=requote_days,
                    group=name
                )
            )
            df.to_csv(STAGED_TARGETS_S3_URI, index=False)

        @task(
            retries=4,  # Higher retries for database queries
            retry_delay=timedelta(minutes=5),  # Moderate delay for DB operations
        )
        def stage_deliverable_file():
            tr = TrinoHook(trino_conn_id="trino_conn")
            df = tr.get_pandas_df(
                sql=deliverable_query.format(
                    SCHEMA=SCHEMA,
                    PREQUOTE_TABLE_NAME=PREQUOTE_TABLE_NAME,
                    ANNOTATED_TABLE_NAME=ANNOTATED_TABLE_NAME,
                    requote_days=requote_days,
                    group=name
                )
            )
            df = df[df['segment_group'].notna()]
            df = df[df['seg_id'].notna()]
            df['seg_id'] = df['seg_id'].astype(int)

            df.to_csv(DELIVERABLE_S3_URI, index=False)

        # with TaskGroup(group_id="create_tables") as create_tables:
        #     create_prequote_table_task = SQLExecuteQueryOperator(
        #         task_id="create_prequote_table",
        #         conn_id="starburst",
        #         sql=CREATE_PREQUOTE_AUDIENCE_TABLE.format(
        #             SCHEMA=SCHEMA,
        #             PREQUOTE_TABLE_NAME=PREQUOTE_TABLE_NAME,
        #             ENV=ENV
        #         ),
        #         retries=3
        #     )
        #     create_annotated_table_task = SQLExecuteQueryOperator(
        #         task_id="create_annotated_table",
        #         conn_id="starburst",
        #         sql=CREATE_ANNOTATED_TABLE.format(
        #             SCHEMA=SCHEMA,
        #             ANNOTATED_TABLE_NAME=ANNOTATED_TABLE_NAME,
        #             ENV=ENV
        #         ),
        #         retries=3
        #     )
        #     create_deliverable_table_task = SQLExecuteQueryOperator(
        #         task_id="create_deliverable_table",
        #         conn_id="starburst",
        #         sql=CREATE_TABLE_DELIVERABLE.format(
        #             SCHEMA=SCHEMA,
        #             DELIVERABLE_TABLE_NAME=DELIVERABLE_TABLE_NAME,
        #             ENV=ENV
        #         ),
        #         reties=2
        #     )

        stage_prequoted_audience_task = stage_prequoted_audience()

        pre_sync_partitions = SQLExecuteQueryOperator(
            task_id=f"sync_partitions_annotate",
            conn_id="starburst",
            sql=f"CALL s3.system.sync_partition_metadata('{SCHEMA}', '{PREQUOTE_TABLE_NAME}', 'ADD')",
            handler=list,
            retries=4,  # Higher retries for database operations
            retry_delay=timedelta(minutes=3),  # Quick retry for partition sync
        )

        stage_quote_file_task = stage_quote_file()

        column_headers = [
            {"index": 0, "type": "address1", "value": "streetaddress"},
            {"index": 1, "type": "zip", "value": "zipcode"}
        ]
        quote_task = quote_file(
            STAGED_TARGETS_S3_URI,
            ANNOTATED_AUDIENCE_S3_URI,
            SELECTED_AUDIENCE_S3_URI,
            ORG_ID,
            column_headers,
            target_file_type="ADDRESS",
            data_source="CLIENT",
            audience_type="B2C",
            chunk_size=500_000,
            env=ENV
        )

        sync_annotated_task = SQLExecuteQueryOperator(
            task_id=f"sync_annotated_task",
            conn_id="starburst",
            sql=f"CALL s3.system.sync_partition_metadata('{SCHEMA}', '{ANNOTATED_TABLE_NAME}', 'ADD')",
            handler=list,
            retries=4,  # Higher retries for database operations
            retry_delay=timedelta(minutes=3),  # Quick retry for partition sync
        )

        stage_deliverable_file_task = stage_deliverable_file()

        sync_deliverable_task = SQLExecuteQueryOperator(
            task_id=f"sync_deliverable_task",
            conn_id="starburst",
            sql=f"CALL s3.system.sync_partition_metadata('{SCHEMA}', '{DELIVERABLE_TABLE_NAME}', 'ADD')",
            handler=list,
            retries=4,  # Higher retries for database operations
            retry_delay=timedelta(minutes=3),  # Quick retry for partition sync
        )

        s3_to_sftp = S3ToSFTPOperator(
            task_id="s3_to_sftp",
            s3_bucket=DELIVERABLE_S3_URI.split("/")[2],
            s3_key=DELIVERABLE_S3_URI.split("/", 3)[3],
            sftp_path=SFTP_PATH,
            sftp_conn_id=SFTP_CONN_ID,
            aws_conn_id="s3_conn",
            retries=5,  # Higher retries for SFTP transfer (critical delivery)
            retry_delay=timedelta(minutes=8),  # Longer delay for SFTP issues
        )

        sync_metrics_task = SQLExecuteQueryOperator(
            task_id=f"insert_metrics",
            conn_id="starburst",
            sql=metrics_insert_query.format(
                env=ENV, config_name=name, tdy=DATE, yst=YESTERDAY
            ),
            handler=list,
            retries=4,  # Higher retries for database operations
            retry_delay=timedelta(minutes=3),  # Quick retry for metrics insert
        )

        @task(
            retries=4,  # Higher retries for database queries
            retry_delay=timedelta(minutes=3),  # Quick retry for metrics
        )
        def stage_metrics_file():
            tr = TrinoHook(trino_conn_id="starburst")
            df = tr.get_pandas_df(
                sql=f"SELECT * FROM s3.{SCHEMA}.{METRICS_TABLE_NAME} WHERE \"group\" = '{name}'"
            )

            df.to_csv(METRICS_S3_URI, index=False)

        stage_metrics_file_task = stage_metrics_file()

        # create_tables >> stage_prequoted_audience_task
        stage_prequoted_audience_task >> pre_sync_partitions
        pre_sync_partitions >> stage_quote_file_task
        stage_quote_file_task >> quote_task
        quote_task >> sync_annotated_task
        sync_annotated_task >> stage_deliverable_file_task
        stage_deliverable_file_task >> sync_deliverable_task
        sync_deliverable_task >> s3_to_sftp
        sync_deliverable_task >> sync_metrics_task >> stage_metrics_file_task


# Create DAG instances
config_1 = {'name': 'prd', 'region_list': "'13', '17', '11', '15', '22'", 'requote_days': '7', 'start_hour': 5}
config_2 = {'name': 'nonprd', 'region_list': "'16', '80', '12', '23', '21'", 'requote_days': '30', 'start_hour': 6}

dag1 = create_toyota_intender_dag(**config_1)
dag2 = create_toyota_intender_dag(**config_2)
