def query_and_upsert_reporting_stats_v2(unique_days):
    import json
    import pandas as pd
    import base64
    import math
    from airflow.providers.trino.hooks.trino import TrinoHook
    from airflow.models import Variable
    from elasticsearch import Elasticsearch, helpers
    from old_dags.xandr_reporting_api.xandr_es_import.queries import (
        reporting_api_query,
        narollupv2,
    )

    trino_hook = TrinoHook(trino_conn_id="trino_conn")

    class CompatElasticsearch(Elasticsearch):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)

            # Override default headers to be backward-compatible with Elasticsearch <8
            self._transport.headers.update(
                {
                    "Content-Type": "application/x-ndjson",
                    "Accept": "application/x-ndjson",
                }
            )

    def get_elasticsearch_credentials():
        """
        Retrieve Elasticsearch credentials from Airflow Variable.
        Returns username and password for authentication.

        Expected Variable format (JSON):
        {
            "username": "elastic_username",
            "password": "elastic_password"
        }
        """
        try:
            # Get credentials from Airflow Variable
            creds = Variable.get("reporting_es_creds", deserialize_json=True)

            username = creds.get("username")
            password = creds.get("password")

            if not username or not password:
                raise ValueError(
                    "Username or password not found in reporting_es_creds variable"
                )

            return username, password

        except Exception as e:
            print(
                f"Error retrieving Elasticsearch credentials from Airflow Variable: {str(e)}"
            )
            raise

    def build_id_for_narollup2(record):
        newtime = record["day"].replace(" ", "_").replace(":", "-")

        key = f"{str(newtime)}_{str(record['org_id'])}_{str(record['campaign_id'])}_{str(record['order_line_id'])}_{str(record['creative_id'])}_{str(record['creative_name'])}_0_0_{str(record['bidder_source'])}"
        if str(record["order_line_id"]) == "--":
            return "", False
        else:
            return key, True

    def upsert_narollup2(df):
        # Get credentials from Airflow Variable
        username, password = get_elasticsearch_credentials()

        es_new = CompatElasticsearch(
            hosts=[
                "https://reporting-elastic-es-reporting.narollup-reportingv2.svc.cluster.local:9200"
            ],
            basic_auth=(username, password),
            verify_certs=False,  # Set to True if you have proper SSL certificates
            request_timeout=100,
        )
        actions = []
        for record in df[narollupv2["columns"]].to_dict(orient="records"):
            # Clean the record data to avoid parsing errors
            cleaned_record = {}
            for key, value in record.items():
                # Handle NaN, None, and infinity values
                if pd.isna(value) or value is None:
                    cleaned_record[key] = None
                elif isinstance(value, float) and (math.isinf(value) or pd.isna(value)):
                    cleaned_record[key] = None
                else:
                    # Convert numpy types to native Python types
                    if hasattr(value, "item"):
                        cleaned_record[key] = value.item()
                    else:
                        cleaned_record[key] = value

            action = {
                "_op_type": "update",
                "_index": "networkanalytics",
                "_id": build_id_for_narollup2(cleaned_record)[0],
                "doc": cleaned_record,
                "doc_as_upsert": True,
            }
            actions.append(action)

        try:
            print(
                f"Attempting to bulk insert {len(actions)} documents to Elasticsearch..."
            )

            # Use streaming_bulk to get detailed error information
            success_count = 0
            errors = []

            # Process all documents, collecting successes and failures
            for ok, result in helpers.streaming_bulk(
                es_new,
                actions,
                index="networkanalytics",
                raise_on_error=False,
                raise_on_exception=False,
            ):
                if ok:
                    success_count += 1
                else:
                    errors.append(result)
                    print(f"❌ Document failed: {result}")

            # Log summary of all operations
            print(f"✅ Successfully processed {success_count} documents")
            print(f"❌ Failed operations: {len(errors)}")

            if errors:
                print("All error details:")
                for i, error in enumerate(errors):
                    try:
                        print(
                            f"   Error {i+1}: {json.dumps(error, indent=2, default=str)}"
                        )
                    except (TypeError, ValueError):
                        print(f"   Error {i+1}: {str(error)}")

                # After processing everything, fail if there were any errors
                print(f"🚨 DAG will fail due to {len(errors)} failed documents")
                raise Exception(
                    f"Elasticsearch bulk operation completed with {success_count} successes and {len(errors)} failures. Check logs above for detailed error information."
                )
            else:
                print("🎉 All operations completed successfully!")

        except Exception as e:
            print(f"❌ Elasticsearch bulk operation failed: {str(e)}")
            print(f"Error type: {type(e).__name__}")
            if hasattr(e, "errors"):
                print(f"Detailed errors: {e.errors[:3]}")  # Show first 3 errors
            raise

    def upsert_date(day):
        # Most of the data preparation is taken care of in the query.\
        with trino_hook.get_conn() as conn:
            print(f"Upserting stats for {day}")
            where_clause = f"day = DATE('{day}')"
            print(conn)
            print(conn.cursor)
            print(vars(conn))
            df = pd.read_sql(
                reporting_api_query.replace(":where_clause", where_clause), conn
            )
        upsert_narollup2(df)

    for day in unique_days:
        upsert_date(day)
