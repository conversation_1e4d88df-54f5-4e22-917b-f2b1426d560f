"""
BI Report Workflow DAG Template

This DAG processes BI reports by executing Trino queries, processing results,
uploading to S3, and sending notifications with comprehensive status callbacks.

Expected Input Configuration (passed via DAG conf):
{
    "execution_id": 123,
    "report_id": 456,
    "report_name": "Daily Sales Report",
    "trino_query": "SELECT * FROM sales WHERE date = current_date",
    "email": "<EMAIL>",
    "large_data_set": false,
    "gzip_compress": true,
    "s3_locations": [
        {
            "s3_bucket": "reports-bucket",
            "s3_key": "daily_reports/sales_2025_01_01.csv"
        }
    ],
    "materialized_view_dependencies": ["mv_sales_summary"],
    "execution_type": "manual",
    "triggered_by": "<EMAIL>",
    "api_callback_url": "https://api.company.com/api/v1/bi-report-executions/123/callback"
}

Callback API Contract:
POST {api_callback_url}
Content-Type: application/json
{
    "status": "running|completed|failed",
    "started_at": "2025-01-01T10:00:00Z",
    "completed_at": "2025-01-01T10:05:00Z",
    "result_s3_path": "s3://bucket/path/file.csv",
    "row_count": 1500,
    "file_size_bytes": 245760,
    "error_message": "Error details if failed",
    "execution_metadata": {"processing_time_seconds": 300}
}
"""

# test_conf =   {
#     "execution_id": 123,
#     "report_id": 456,
#     "report_name": "test_1",
#     "trino_query": "SELECT * FROM s3.dev_prototyping.auto_4eyes_segments limit 10;",
#     "email": "<EMAIL>",
#     "large_data_set": False,
#     "gzip_compress": False,
#     "s3_locations": [
#         {
#             "s3_bucket": "vr-timestamp",
#             "s3_key": "bi_sources/test/bi_config_test/{TODAY}/{YESTERDAY}/date_test.csv"
#         }
#     ],
#     "materialized_view_dependencies": [],
#     "execution_type": "manual",
#     "triggered_by": "<EMAIL>",
#     "api_callback_url": "https://bi-api.k8s.eltoro.com/api/v1/bi-report-executions/1/callback"
# }

from datetime import datetime, timedelta, timezone
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.email import EmailOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
import requests
import json
import logging
import gzip
import tempfile
from typing import Dict, Any

from airflow.providers.trino.hooks.trino import TrinoHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from pygene.base_api import NextGenBaseAPI
import time
from airflow.exceptions import AirflowFailException
from etdag import ETDAG

# DAG Configuration
DEFAULT_ARGS = {
    'owner': 'data-services',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'execution_timeout': timedelta(hours=2),
}

# Environment-specific configuration
TRINO_CONNECTION_ID = "starburst" #Variable.get("trino_connection_id", "trino_default")
AWS_CONNECTION_ID = "s3_conn" #Variable.get("aws_connection_id", "aws_default")

logger = logging.getLogger(__name__)

def make_request_with_token_renewal(method, url, env, max_retries=5, **kwargs):
    pygene_creds = json.loads(Variable.get(f"dataservices_gene_creds_{env}"))
    client_id = pygene_creds["client_id"]
    client_secret = pygene_creds["client_secret"]
    base_api = NextGenBaseAPI(client_id, client_secret, env=env)

    headers = {
        "Authorization": f"Bearer {base_api.access_token}",
        "Content-Type": "application/json",
    }
    kwargs["headers"] = headers

    for attempt in range(1, max_retries + 1):
        try:
            response = requests.request(method, url, timeout=10, **kwargs)
            if base_api._resp_handler(response):
                print(f"Attempt {attempt}: Refreshing token and retrying...")
                base_api.refresh_token()
                headers["Authorization"] = f"Bearer {base_api.access_token}"
                kwargs["headers"] = headers
                response = requests.request(method, url, timeout=10, **kwargs)

            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            print(f"Attempt {attempt} failed: {e}")
            time.sleep(180)  # Wait for 3 minutes before retrying
            if attempt == max_retries:
                raise AirflowFailException(
                    f"Request to {url} failed after {max_retries} attempts: {e}"
                )

    return response

def send_status_callback(context: Dict[str, Any], status: str, **kwargs) -> None:
    """Send status update to the BI API callback endpoint"""
    conf = context['dag_run'].conf
    callback_url = conf.get('api_callback_url')
    execution_id = conf.get('execution_id')
    
    if not callback_url:
        logger.warning(f"No callback URL provided for execution {execution_id}")
        return
    
    # Prepare callback payload
    payload = {
        'status': status,
        **kwargs
    }
    
    # Add timestamps
    if status == 'running' and 'started_at' not in payload:
        payload['started_at'] = datetime.now(timezone.utc).isoformat()
    elif status in ['completed', 'failed'] and 'completed_at' not in payload:
        payload['completed_at'] = datetime.now(timezone.utc).isoformat()
    
    try:
        response = make_request_with_token_renewal("POST", callback_url, env="prod", json=payload) #hardcode env="prod" for now
        logger.info(f"Status callback sent successfully: {status} for execution {execution_id}")
        logger.info(f"Response: {response.json()}")
    except Exception as e:
        logger.error(f"Failed to send status callback: {str(e)}")
        # Don't fail the task if callback fails
        pass

def start_execution(**context) -> None:
    """Mark execution as started and validate inputs"""
    conf = context['dag_run'].conf
    
    # Validate required fields
    required_fields = ['execution_id', 'report_id', 'report_name', 'trino_query', 'email', 'api_callback_url']
    missing_fields = [field for field in required_fields if not conf.get(field)]
    
    if missing_fields:
        error_msg = f"Missing required configuration fields: {missing_fields}"
        logger.error(error_msg)
        send_status_callback(context, 'failed', error_message=error_msg)
        raise ValueError(error_msg)
    
    logger.info(f"Starting BI report execution {conf['execution_id']} for report '{conf['report_name']}'")
    
    # Send running status callback
    send_status_callback(context, 'running')

def refresh_materialized_views(**context) -> None:
    """Refresh materialized view dependencies if specified"""
    conf = context['dag_run'].conf
    mv_dependencies = conf.get('materialized_view_dependencies', [])
    
    if not mv_dependencies:
        logger.info("No materialized view dependencies to refresh")
        return
    
    logger.info(f"Refreshing materialized views: {mv_dependencies}")
    
    trino_hook = TrinoHook(trino_conn_id=TRINO_CONNECTION_ID)
    for mv in mv_dependencies:
        trino_hook.run(f"REFRESH MATERIALIZED VIEW {mv}")
    
    logger.info("Materialized view refresh completed")

def execute_and_upload(**context) -> Dict[str, Any]:
    """Execute the Trino query and upload results to S3 locations"""
    conf = context['dag_run'].conf
    
    # Extract parameters
    query = conf['trino_query']
    large_data_set = conf.get('large_data_set', False)
    gzip_compress = conf.get('gzip_compress', True)
    s3_locations = conf.get('s3_locations', [])
    
    if not s3_locations:
        error_msg = "No S3 locations specified"
        logger.error(error_msg)
        send_status_callback(context, 'failed', error_message=error_msg)
        raise ValueError(error_msg)
    
    logger.info(f"Executing Trino query for execution {conf['execution_id']}")
    logger.info(f"Query: {query}")
    logger.info(f"Large dataset mode: {large_data_set}")
    
    try:
        # Process based on dataset size
        if large_data_set:
            logger.info("Processing large dataset in chunks")
            df_chunks = TrinoHook(trino_conn_id=TRINO_CONNECTION_ID).get_pandas_df_by_chunks(
                sql=query, chunksize=1_000_000
            )
            with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
                temp_file_path = temp_file.name
            
            row_count = 0
            for i, df_chunk in enumerate(df_chunks):
                chunk_rows = len(df_chunk)
                row_count += chunk_rows
                logger.info(f"Writing chunk {i} with {chunk_rows} rows")
                if i == 0:
                    df_chunk.to_csv(temp_file_path, index=False)
                else:
                    df_chunk.to_csv(temp_file_path, mode="a", header=False, index=False)
        else:
            logger.info("Processing standard dataset")
            df = TrinoHook(trino_conn_id=TRINO_CONNECTION_ID).get_pandas_df(sql=query)
            row_count = len(df)
            with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
                temp_file_path = temp_file.name
            df.to_csv(temp_file_path, index=False)

        # Compress if needed
        if gzip_compress:
            logger.info("Compressing output file")
            compressed_file_path = temp_file_path + ".gz"
            with open(temp_file_path, "rb") as f_in:
                with gzip.open(compressed_file_path, "wb") as f_out:
                    import shutil
                    shutil.copyfileobj(f_in, f_out)
            file_to_upload = compressed_file_path
            # Clean up the uncompressed file
            import os
            os.remove(temp_file_path)
        else:
            file_to_upload = temp_file_path
        
        # Upload to all S3 locations
        s3_hook = S3Hook(aws_conn_id=AWS_CONNECTION_ID)
        uploaded_files = []
        total_file_size = 0
        
        for s3_location in s3_locations:
            bucket = s3_location['s3_bucket']
            key = s3_location['s3_key']

            #Replace {TODAY}, {YESTERDAY} with relative dates
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)
            key = key.replace('{TODAY}', today.strftime('%Y-%m-%d')).replace('{YESTERDAY}', yesterday.strftime('%Y-%m-%d'))
            
            # Add .gz extension if compressing and not already present
            if gzip_compress and not key.endswith('.gz'):
                key += '.gz'
            
            logger.info(f"Uploading to s3://{bucket}/{key}")
            
            # Get file size for metadata
            import os
            file_size = os.path.getsize(file_to_upload)
            total_file_size += file_size
            
            s3_hook.load_file(
                filename=file_to_upload,
                key=key,
                bucket_name=bucket,
                replace=True,
            )
            
            uploaded_files.append({
                'bucket': bucket,
                'key': key,
                'sizeBytes': file_size,
                's3Path': f's3://{bucket}/{key}'
            })
            
            logger.info(f"Successfully uploaded to s3://{bucket}/{key} ({file_size} bytes)")
        
        # Clean up temporary file
        import os
        if os.path.exists(file_to_upload):
            os.remove(file_to_upload)
        
        # Prepare metadata for XCom
        upload_metadata = {
            'uploadedFiles': uploaded_files,
            'totalFileSizeBytes': total_file_size,
            'rowCount': row_count,
            'compressionEnabled': gzip_compress
        }
        
        # Store metadata in XCom for final callback
        context['task_instance'].xcom_push(key='upload_metadata', value=upload_metadata)
        
        return upload_metadata
        
    except Exception as e:
        error_msg = f"Query execution and upload failed: {str(e)}"
        logger.error(error_msg)
        send_status_callback(context, 'failed', error_message=error_msg)
        raise

def send_completion_notification(**context) -> None:
    """Send email notification and final status callback"""
    conf = context['dag_run'].conf
    upload_metadata = context['task_instance'].xcom_pull(task_ids='execute_and_upload', key='upload_metadata')
    
    execution_id = conf['execution_id']
    
    # Prepare completion callback
    primary_s3_path = upload_metadata['uploadedFiles'][0]['s3Path'] if upload_metadata['uploadedFiles'] else None
    
    completion_data = {
        'result_s3_path': primary_s3_path,
        'row_count': upload_metadata['rowCount'],
        'file_size_bytes': upload_metadata['totalFileSizeBytes'],
        'execution_metadata': {
            'uploaded_files': upload_metadata['uploadedFiles'],
            'compression_enabled': upload_metadata['compressionEnabled'],
            'processing_completed_at': datetime.now(timezone.utc).isoformat()
        }
    }
    
    # Send completion callback
    send_status_callback(context, 'completed', **completion_data)
    
    logger.info(f"BI report execution {execution_id} completed successfully")

def handle_failure(context) -> None:
    """Handle task failures and send failure callback"""
    conf = context['dag_run'].conf
    
    # Get failure information
    task_instance = context.get('task_instance')
    exception = context.get('exception')
    
    error_message = str(exception) if exception else "Unknown error occurred"
    
    logger.error(f"BI report execution {conf['execution_id']} failed: {error_message}")
    
    # Send failure callback
    send_status_callback(
        context, 
        'failed', 
        error_message=error_message,
        execution_metadata={
            'failed_task': task_instance.task_id if task_instance else 'unknown',
            'failure_timestamp': datetime.now(timezone.utc).isoformat()
        }
    )

# Create the DAG
with ETDAG(
    dag_id='bi_report_workflow',
    default_args=DEFAULT_ARGS,
    description='BI Report Processing Workflow',
    schedule_interval=None,  # Triggered externally
    catchup=False,
    max_active_runs=10,
    tags=['bi-reports', 'data-processing'],
) as dag:

    # Task definitions
    start_task = PythonOperator(
        task_id='start_execution',
        python_callable=start_execution,
        dag=dag,
        on_failure_callback=handle_failure,
    )

    refresh_mv_task = PythonOperator(
        task_id='refresh_materialized_views',
        python_callable=refresh_materialized_views,
        dag=dag,
        on_failure_callback=handle_failure,
    )

    execute_and_upload_task = PythonOperator(
        task_id='execute_and_upload',
        python_callable=execute_and_upload,
        dag=dag,
        on_failure_callback=handle_failure,
    )

    completion_task = PythonOperator(
        task_id='send_completion',
        python_callable=send_completion_notification,
        dag=dag,
        on_failure_callback=handle_failure,
    )

    # Task dependencies
    start_task >> refresh_mv_task >> execute_and_upload_task >> completion_task