import pandas as pd
import numpy as np


def prepare_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Applies centralized missing value imputation using constants from Starburst.
    Cleans, fills, types, and creates features on the selected columns only.
    """
    df_filled = df.copy()

    # mortgage flag BEFORE filtering
    if "first_position_mortgage_amount" in df_filled.columns:
        df_filled["has_mortgage"] = np.where(
            df_filled["first_position_mortgage_amount"].notna(), 1, 0
        )

    # log transforms
    log_col_map = {
        "estimated_value_mktg": "log_estimated_value",
        "estimated_equity": "log_estimated_equity",
    }
    for col, log_col in log_col_map.items():
        if col in df_filled.columns:
            df_filled[log_col] = np.log1p(
                pd.to_numeric(df_filled[col], errors="coerce").fillna(0)
            )

    # mortgage age binning
    if "days_since_first_mortgage" in df_filled.columns:
        df_filled["mortgage_age_category"] = pd.cut(
            df_filled["days_since_first_mortgage"],
            bins=[0, 1825, 5475, np.inf],
            labels=["<5 years", "5-15 years", ">15 years"],
        )

    # cleaning number of buildings
    if "number_of_buildings" in df_filled.columns:
        df_filled["number_of_buildings"] = df_filled["number_of_buildings"].fillna(0)

    # Convert bools to int
    bool_cols = df_filled.select_dtypes(include=["bool"]).columns
    for col in bool_cols:
        df_filled[col] = df_filled[col].astype(int)

    # Final type casting

    column_type_dict = {
        "number_of_buildings": int,
        "estimated_value_high_mktg": float,
        "estimated_value_low_mktg": float,
        "first_position_mortgage_ltv_loan_to_value": float,
        "first_position_mortgage_amount": float,
        "first_position_mortgage_interest_rate": float,
    }
    for col, dtype in column_type_dict.items():
        if col in df_filled.columns:
            df_filled[col] = df_filled[col].astype(dtype)

    if "state" in df_filled.columns:
        df_filled.rename(columns={"state": "state_code"}, inplace=True)

    final_cols = [
        "days_since_first_mortgage",
        "heloc_model_propensity_score",
        "first_position_mortgage_amount",
        "has_mortgage",
        "cbsa_type",
        "mortgage_age_category",
        "log_estimated_equity",
        "purchase_mortgage_model_propensity_score",
        "state_code",
        "median_listing_price",
        "median_search_range",
        "average_square_feet",
        "how_recently_shopping",
        "avg_days_after_listing_date",
        "sq_ft_range",
        "days_shopping",
        "stddev_listing_price",
        "distinct_listings",
        "new_homeshopper",
        "total_obs",
        "homeowner",
        "owner_occupancy_code",
        "homeshopper",
        "log_estimated_value",
    ]

    df_filled = df_filled[final_cols]
    return df_filled
