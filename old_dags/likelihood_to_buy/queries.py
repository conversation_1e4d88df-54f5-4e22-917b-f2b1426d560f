ltb_query = """
SELECT 
*,
CASE WHEN total_obs is null THEN false ELSE true END as homeshopper
FROM s3.gold_real_estate_intender.likelihood_to_buy_vol_lien lbvl
FULL OUTER JOIN s3.gold_real_estate_intender.likelihood_to_buy_hs USING(clip, ethashv2, ethashv1)
"""


vol_lien_query = """
create table if not exists s3.dev_data_science.likelihood_to_buy_vol_lien as 
with latest_vol_lien_date as (
    select max(partition.file_transfer_date) as latest_date
    from "olympus"."bronze_corelogic"."vol_lien_status_m2_dpc$partitions"
),
clip_state_mapping as (
    select 
        ceb.clip,
        ceb.state,
        ceb.ethashv1,
        ceb.ethashv2
    from olympus.silver_corelogic.clip_ethash_bridge ceb
),
latest_vol_lien as (
    select 
        vl.clip,
        try(date_parse(vl.first_position_mortgage_date, '%Y%m%d')) as first_position_mortgage_date,
        vl.owner_occupancy_code,
        vl.file_transfer_date,
        vl.estimated_value_mktg,
        vl.estimated_equity,
        first_position_mortgage_amount,
        cbsa_type
    from olympus.bronze_corelogic.vol_lien_status_m2_dpc vl
    join latest_vol_lien_date lvd 
        on vl.file_transfer_date = lvd.latest_date
    where 
        vl.owner_occupancy_code in ('O', 'S', 'M') 
        and (vl.owner_1_corporate_indicator is null or vl.owner_2_corporate_indicator is null)
),
filtered_propensity as (
    select 
        ps.clip,
        ps.heloc_model_propensity_score,
        ps.purchase_mortgage_model_propensity_score
    from olympus.silver_corelogic.propensityscore ps
    where ps.propensity_score_run_date = (
        select max(ps_inner.propensity_score_run_date) 
        from olympus.silver_corelogic.propensityscore ps_inner 
        where ps_inner.clip = ps.clip
    )
)
select 
    vl.clip,
    ceb.ethashv1,
    ceb.ethashv2,
    CASE WHEN vl.clip IS NOT NULL THEN true ELSE false END AS homeowner,
    date_diff('day', vl.first_position_mortgage_date, current_date) as days_since_first_mortgage,
    case 
        when vl.first_position_mortgage_date is null then 'unknown'
        when date_diff('year', vl.first_position_mortgage_date, current_date) < 5 then 'young'
        when date_diff('year', vl.first_position_mortgage_date, current_date) between 5 and 15 then 'mid'
        else 'old'
    end as mortgage_age_category,
    first_position_mortgage_amount,
    cbsa_type,
    vl.owner_occupancy_code,
    fp.heloc_model_propensity_score,
    fp.purchase_mortgage_model_propensity_score,
    ceb.state,
    vl.estimated_value_mktg,
    vl.estimated_equity,
    vl.file_transfer_date
from clip_state_mapping ceb 
left join filtered_propensity fp 
    on ceb.clip = fp.clip
left join latest_vol_lien vl
    on vl.clip = ceb.clip;
    """

homeshopper_query = """
create table if not exists s3.dev_data_science.likelihood_to_buy_hs as 
with device_listing_agg_filtered as (
    select
        hh.ethashv2 as visitor_ethashv2,
        obs.clip,
        obs.deviceid,
        obs.zipcode,
        obs.listing_price,
        obs.square_feet,
        obs.listing_date,
        obs.off_market_date,
        count(obs.timestamp) as days_seen,
        DATE_DIFF('day', obs.listing_date,MIN("timestamp")) AS days_after_listing_date,
        min(obs.timestamp) as first_seen_date,
        max(obs.timestamp) as last_seen_date
    from s3.gold_real_estate_intender.device_obs_w_listings_90_days obs
    inner join s3.gold_real_estate_intender.device_ethash hh
        on hh.deviceid = obs.deviceid 
    group by 
    hh.ethashv2,
    obs.clip, 
    obs.deviceid,
    obs.zipcode,
    obs.listing_price,
    obs.square_feet,
    obs.listing_date,
    obs.off_market_date
),
household_listing_agg_filtered_otm as (
    -- Aggregate at the household level
    select
    visitor_ethashv2, 
    obs.clip, 
    obs.zipcode,
    listing_price,
    square_feet,
    listing_date,
    off_market_date,
    6371 * 2 * ASIN(
      SQRT(
          POWER(SIN(RADIANS(zip_visitor.latitude - zip_listing.latitude) / 2), 2) + 
          COS(RADIANS(zip_visitor.latitude)) * COS(RADIANS(zip_listing.latitude)) * 
          POWER(SIN(RADIANS(zip_visitor.longitude - zip_listing.longitude) / 2), 2)
      )) as distance_km,   
        BOOL_OR(days_seen > 1) AS repeat_visitor, -- fixed the error by removing the nested aggregate function
        sum(days_seen) as total_obs,
        min(days_after_listing_date) as days_after_listing_date,
        
        min(first_seen_date) as first_seen,
        max(last_seen_date) as last_seen
    from device_listing_agg_filtered obs
      LEFT JOIN  "s3"."silver_corelogic"."clip_ethash_bridge" zip_visitor 
      ON obs.visitor_ethashv2 = zip_visitor.ethashv2
  LEFT JOIN 
      "s3"."silver_corelogic"."clip_ethash_bridge" zip_listing 
      ON zip_listing.clip = obs.clip
    group by 1, 2, 3, 4, 5, 6, 7,8

),
distinct_household as (
    -- Final household-level aggregation
    select
        dh.visitor_ethashv2,
        count(dh.clip) as distinct_listings,
        sum(dh.total_obs) as total_obs,
        min(dh.first_seen) as first_seen,
        max(dh.last_seen) as last_seen,
        bool_or(dh.repeat_visitor) AS repeat_visitor, 
        approx_percentile(distance_km, 0.5) AS median_search_range,
        approx_percentile(listing_price, 0.5) AS median_listing_price,
        min(days_after_listing_date) as min_days_after_listing_date,
        max(days_after_listing_date) as max_days_after_listing_date,
        avg(days_after_listing_date) as avg_days_after_listing_date,
        approx_percentile(days_after_listing_date, 0.5) as median_days_after_listing_date,
        stddev(listing_price) stddev_listing_price,
        avg(square_feet) as average_square_feet,
        min(square_feet) min_sq_ft,
        max(square_feet) max_sq_ft
    from household_listing_agg_filtered_otm dh
    group by dh.visitor_ethashv2
),
final_hs_table as (
    select 
        ceb.clip,  
        ceb.ethashv1,
        ceb.ethashv2,
        dh.total_obs,
        dh.distinct_listings,
        dh.repeat_visitor,
        average_square_feet,
        stddev_listing_price,
        median_listing_price,
        median_search_range,
        avg_days_after_listing_date,
        max_sq_ft - min_sq_ft as sq_ft_range,
        date_diff('day', dh.first_seen, current_date) as days_shopping, 
        date_diff('day', dh.last_seen, current_date) as how_recently_shopping, 
        case 
            when dh.total_obs is null then false 
            when date_diff('day', dh.first_seen, current_date) <= 14 then true 
            else false 
        end as new_homeshopper
    from distinct_household dh
    inner join olympus.silver_corelogic.clip_ethash_bridge ceb 
        on dh.visitor_ethashv2 = ceb.ethashv2 
    where dh.total_obs < 50  
)
select * from final_hs_table;
"""
