from airflow.decorators import task
from datetime import datetime, timed<PERSON>ta
from etdag import ETDAG
import awswrangler as wr
from airflow.providers.trino.hooks.trino import TrinoHook
from old_dags.likelihood_to_buy.queries import ltb_query
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator

"""
# Likelihood to buy - Workflow DAG

## Overview
This DAG orchestrates the **LTB Predictions workflow**, which processes large datasets using Trino, applies ML models 
to generate prediction outputs, and writes the results to a partitioned Parquet dataset in S3 for downstream querying via Trino.

The DAG reads data in large chunks via Trino, transforms and enriches each chunk using a set of ML models, 
and then writes results to partitioned S3 paths based on `run_date`. Each chunk is processed independently to support scalability, fault tolerance, and retriability.

## Execution & Triggering
- This DAG **runs every day at 6 AM UTC**.
- It can be triggered manually if needed.
- No custom parameters are currently required at trigger time.
- The DAG uses the current date to determine the `run_date` partition for S3 outputs.

## Concurrency & Parallelism
- Trino queries are executed in **chunks** of 1.5 million rows to support large-scale data processing.
- Each chunk is saved as a Parquet file and processed **in parallel** via Airflow's task mapping (`expand()`).
- This allows multiple chunks to be transformed and written to S3 simultaneously.
- Files are written safely using partitioned S3 paths to avoid overwrite conflicts.

## Stages of Execution
1. **Trino Query Execution (Chunked):** The initial task queries a Trino source table in 1.5M row chunks and stores each chunk as a local Parquet file.
2. **Chunk Processing:** Each chunk is read from disk, transformed using the `prepare_data()` function, enriched with ML model predictions, and formatted for output.
3. **S3 Write:** Each processed chunk is written to a partitioned S3 path based on `run_date` using the AWS Wrangler library.
4. **Partition Sync:** Downstream processes can sync new partitions for Trino after DAG completion.

## Retry & Failure Handling
- Each task, including chunk processing, is **retriable independently**.
- This ensures a failure in one chunk does not affect others and enables partial reruns with data integrity.
- A **single retry** is allowed per task, with a delay of 1 minute between retries.
- Partitioned writes and chunk isolation provide safe, idempotent reprocessing.

## Owners
- **BP** & **Elizabeth**
"""


default_args = {
    "owner": "BP",
    "max_active_tis_per_dag": 5,
    "retries": 1,
    "retry_delay": timedelta(minutes=1),
}


################

# Chunks the query and writes to a temp file
# Once all chunks are written, it will pass the paths to the next task


################
@task
def get_chunks():

    hook = TrinoHook(trino_conn_id="trino_conn")
    df_chunks = hook.get_pandas_df_by_chunks(sql=ltb_query, chunksize=1_500_000)

    s3_base_path = "s3://vr-timestamp/bi_sources/ltb_predictions/tmp_files/"
    paths = []
    for i, chunk in enumerate(df_chunks):
        if chunk.empty:
            continue

        s3_path = f"{s3_base_path}chunk_{i}.csv"
        print(f"Writing chunk {i} to {s3_path}")
        wr.s3.to_csv(df=chunk, path=s3_path, index=False)
        paths.append(s3_path)

    return paths


################

# Processes the DF chunk against the LTB model in mlflow
# Once processed it will load the parquet file to S3

################


@task.virtualenv(
    requirements="requirements.txt",
    system_site_packages=True,
    max_active_tis_per_dag=5,
    # python_version="3.10"
)
def process_chunk(chunk_path: str):
    import mlflow
    import pandas as pd
    import awswrangler as wr
    from airflow.models import Variable
    from old_dags.likelihood_to_buy.prepare_data import prepare_data
    import logging
    import time
    import numpy as np

    env = Variable.get("environment")

    logging.basicConfig(
        filename="processing_times.log",
        level=logging.INFO,
        format="%(asctime)s - %(message)s",
    )
    mlflow.set_tracking_uri(uri="https://mlflow.k8s.eltoro.com")

    # Measure time taken to load models
    start_time_model_loading = time.time()

    ltb_90_model = mlflow.sklearn.load_model(model_uri=f"models:/ltb_90@prod")
    ltb_180_model = mlflow.sklearn.load_model(model_uri=f"models:/ltb_180@prod")
    column_encoder = mlflow.sklearn.load_model(model_uri=f"models:/ltb_encoder@prod")
    scaler_encoder = mlflow.sklearn.load_model(
        model_uri=f"models:/likelihood_to_buy_scaler@prod"
    )
    end_time_model_loading = time.time()
    elapsed_time_model_loading = end_time_model_loading - start_time_model_loading
    logging.info(f"Time taken to load models: {elapsed_time_model_loading:.2f} seconds")
    start_time_preprocessing = time.time()

    df_chunk = wr.s3.read_csv(
        chunk_path,
        dtype={
            "clip": "object",
            "first_position_mortgage_amount": "object",
            "estimated_value_mktg": "object",
            "estimated_equity": "object",
        },
    )
    df_chunk.set_index(["clip", "ethashv1", "ethashv2"], inplace=True)

    test_df = prepare_data(df_chunk)  # Assume it's imported
    # Add your ML predictions here
    test_df["transaction_class"] = None
    encoded_df = column_encoder.transform(test_df)

    transformed_cols = (
        column_encoder.named_transformers_["oe"].get_feature_names_out().tolist()
        if hasattr(column_encoder.named_transformers_["oe"], "get_feature_names_out")
        else None
    )
    passthrough_cols = [col for col in test_df.columns if col not in transformed_cols]

    clean_test = pd.DataFrame(encoded_df, columns=transformed_cols + passthrough_cols)
    clean_test.set_index(test_df.index, inplace=True)
    clean_test.drop(columns=["transaction_class"], inplace=True)

    # Replace infinite values with NaN (silly log transform)
    clean_test.replace([np.inf, -np.inf], np.nan, inplace=True)

    # Ensure only expected columns are used
    expected_columns = scaler_encoder.feature_names_in_
    clean_test = clean_test[expected_columns]

    # Scale the data
    scale_test = scaler_encoder.transform(clean_test)

    # End timing
    end_time_preprocessing = time.time()
    elapsed_time_preprocessing = end_time_preprocessing - start_time_preprocessing
    logging.info(
        f"Time taken for preprocessing chunk  {elapsed_time_preprocessing:.2f} seconds"
    )

    # 90-day predictions
    start_time_90 = time.time()

    ltb_90_prob = ltb_90_model.predict_proba(scale_test)[:, 1]
    df_chunk["ltb_90_prob"] = ltb_90_prob
    df_chunk["ltb_90_pred"] = df_chunk["ltb_90_prob"].apply(
        lambda x: 1 if x >= 0.65 else 0
    )

    end_time_90 = time.time()
    elapsed_time_90 = end_time_90 - start_time_90
    logging.info(
        f"Time taken for 90-day predictions for chunk starting at: {elapsed_time_90:.2f} seconds"
    )

    # 180-day predictions
    start_time_180 = time.time()

    ltb_180_prob = ltb_180_model.predict_proba(scale_test)[:, 1]
    df_chunk["ltb_180_prob"] = ltb_180_prob
    df_chunk["ltb_180_pred"] = df_chunk["ltb_180_prob"].apply(
        lambda x: 1 if x >= 0.8 else 0
    )

    end_time_180 = time.time()
    elapsed_time_180 = end_time_180 - start_time_180
    logging.info(
        f"Time taken for 180-day predictions for chunk starting at: {elapsed_time_180:.2f} seconds"
    )

    # Reset index and select final columns
    df_reset = df_chunk.reset_index()[
        [
            "clip",
            "ethashv1",
            "ethashv2",
            "ltb_90_prob",
            "ltb_90_pred",
            "ltb_180_prob",
            "ltb_180_pred",
            "days_since_first_mortgage",
        ]
    ]

    df_reset.loc[
        df_reset["ethashv2"].isnull(),
        ["ltb_90_prob", "ltb_90_pred", "ltb_180_prob", "ltb_180_pred"],
    ] = None

    ## Bought a home in the last 6 months

    df_reset.loc[
        df_reset["days_since_first_mortgage"] <= 180,
        ["ltb_90_prob", "ltb_90_pred", "ltb_180_prob", "ltb_180_pred"],
    ] = 0

    df_reset["run_date"] = pd.to_datetime("today").strftime("%Y-%m-%d")

    final_columns = [
        "clip",
        "ethashv1",
        "ethashv2",
        "run_date",
        "ltb_90_prob",
        "ltb_90_pred",
        "ltb_180_prob",
        "ltb_180_pred",
    ]

    # Save to S3
    logging.info(
        f"Uploading to s3://vr-timestamp/bi_sources/ltb_predictions/{env}/run_date={pd.to_datetime('today').strftime('%Y-%m-%d')}/"
    )
    wr.s3.to_parquet(
        df=df_reset[final_columns],
        path=f"s3://vr-timestamp/bi_sources/ltb_predictions/{env}/",
        dataset=True,
        mode="append",
        partition_cols=["run_date"],
    )


with ETDAG(
    dag_id="likelihood_to_buy",
    schedule="0 6 * * *",
    default_args=default_args,
    catchup=False,
    description="Likelihood to Buy DAG - performs LTB predictions",
    start_date=datetime(2025, 3, 18),
    et_failure_msg=True,
    tags=["LTB", "DS", "Prod"],
) as dag:

    refresh_hs = SQLExecuteQueryOperator(
        task_id="refresh_hs",
        conn_id="trino_conn",
        sql='REFRESH MATERIALIZED VIEW  "s3"."gold_real_estate_intender"."likelihood_to_buy_hs"',
    )

    chunk_paths = get_chunks()
    expanded = process_chunk.expand(chunk_path=chunk_paths)

    sync_table = SQLExecuteQueryOperator(
        task_id="sync_table",
        conn_id="trino_conn",
        sql="CALL s3.system.sync_partition_metadata('gold_real_estate_intender', 'prod_ltb_predictions', 'ADD')",
    )

    refresh_hs >> chunk_paths >> expanded >> sync_table
