"""
Team utility functions for ETDAG framework.

This module provides utility functions for team-based
notification routing and configuration lookup.
"""

from typing import List, Optional

from et_config.teams.mappings import (
    OPSGENIE_TEAM_MAPPING,
    TEAM_EMAIL_MAPPING,
    TEAM_SLACK_MAPPING,
)


def get_team_emails(team: str) -> List[str]:
    """Get email list for a team.

    Args:
        team: Team name

    Returns:
        List of email addresses for the team
    """
    return TEAM_EMAIL_MAPPING.get(team, [f"{team}@eltoro.com"])


def get_team_slack_channel(team: str) -> str:
    """Get Slack channel for a team.

    Args:
        team: Team name

    Returns:
        Slack channel for the team
    """
    return TEAM_SLACK_MAPPING.get(team, "#data-alerts")


def get_opsgenie_team(team: str) -> Optional[str]:
    """Get OpsGenie team name for a team.

    DEPRECATED: JSM Operations now uses single team routing.
    This function is kept for backward compatibility only.

    Args:
        team: Team name

    Returns:
        OpsGenie team name or None if not found
    """
    return OPSGENIE_TEAM_MAPPING.get(team)


def is_valid_team(team: str) -> bool:
    """Check if a team is configured in any mapping.

    Args:
        team: Team name to check

    Returns:
        True if team is found in any mapping
    """
    return (
        team in TEAM_EMAIL_MAPPING
        or team in TEAM_SLACK_MAPPING
        or team in OPSGENIE_TEAM_MAPPING
    )


def get_all_teams() -> List[str]:
    """Get list of all configured teams.

    Returns:
        List of all team names across all mappings
    """
    all_teams = set()
    all_teams.update(TEAM_EMAIL_MAPPING.keys())
    all_teams.update(TEAM_SLACK_MAPPING.keys())
    all_teams.update(OPSGENIE_TEAM_MAPPING.keys())
    return sorted(list(all_teams))


def get_team_info(team: str) -> dict:
    """Get comprehensive information about a team.

    Args:
        team: Team name

    Returns:
        Dictionary with team configuration information
    """
    return {
        "team": team,
        "emails": get_team_emails(team),
        "slack_channel": get_team_slack_channel(team),
        "opsgenie_team": get_opsgenie_team(
            team
        ),  # DEPRECATED: Use JSM Operations instead
        "is_configured": is_valid_team(team),
    }
