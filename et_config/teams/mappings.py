"""
Team mappings for notifications and routing.

This module contains the mappings between teams and their
notification preferences including email, Slack, and JSM Operations.
"""

from typing import Dict, List

# Team email mappings
TEAM_EMAIL_MAPPING: Dict[str, List[str]] = {
    "data-engineering": ["<EMAIL>"],
    "analytics-engineering": ["<EMAIL>"],
    "business-intelligence": ["<EMAIL>"],
    "client-services": ["<EMAIL>"],
    "platform-team": ["<EMAIL>"],
}

# Team Slack channel mappings
TEAM_SLACK_MAPPING: Dict[str, str] = {
    "data-engineering": "#data-engineering-guild",
    "analytics-engineering": "#analytics-engineering-guild",
    "business-intelligence": "#business-intelligence-guild",
    "client-services": "#client-alerts",
    "platform-team": "#platform-alerts",
}


def get_team_email_mapping() -> Dict[str, List[str]]:
    """Get the complete team email mapping."""
    return TEAM_EMAIL_MAPPING.copy()


def get_team_slack_mapping() -> Dict[str, str]:
    """Get the complete team Slack mapping."""
    return TEAM_SLACK_MAPPING.copy()


def add_team_email(team: str, emails: List[str]) -> None:
    """Add or update team email mapping.

    Args:
        team: Team name
        emails: List of email addresses
    """
    TEAM_EMAIL_MAPPING[team] = emails


def add_team_slack(team: str, channel: str) -> None:
    """Add or update team Slack channel mapping.

    Args:
        team: Team name
        channel: Slack channel (with or without #)
    """
    if not channel.startswith("#"):
        channel = f"#{channel}"
    TEAM_SLACK_MAPPING[team] = channel
