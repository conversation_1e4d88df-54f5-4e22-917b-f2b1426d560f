"""
Notification configuration models for ETDAG framework.

This module contains data classes for configuring notifications
including Slack, email, and OpsGenie settings.
"""

from dataclasses import dataclass
from typing import List, Optional, Union


@dataclass
class NotificationConfig:
    """Configuration for notifications and alerting."""

    # Slack configuration
    slack_on_success: bool = False
    slack_on_failure: bool = True
    slack_channel_override: Optional[str] = None

    # Email configuration
    email_on_success: bool = False
    email_on_failure: bool = False
    email_destination_override: Optional[Union[str, List[str]]] = None

    # JSM Operations configuration
    jsm_operations_alert: bool = False
    jsm_operations_priority: str = (
        "P3"  # Priority level (P1=Critical, P2=High, P3=Medium, P4=Low, P5=Lowest)
    )
    team_owner: Optional[str] = None  # Team owner for routing

    def get_default_slack_channel(self, environment: str) -> str:
        """Get the default Slack channel based on environment."""
        # Use environment-specific default
        if environment == "local":
            return "#dev-airflow-notifications-local"
        elif environment == "dev":
            return "#dev-airflow-notifications-dev"
        else:
            return "#dev-airflow-notifications-prod"

    def get_slack_channel(self, environment: str) -> str:
        """Get the appropriate Slack channel."""
        return self.slack_channel_override or self.get_default_slack_channel(
            environment
        )

    def get_email_destination(self, environment: str) -> List[str]:
        """Get the appropriate email destination based on environment."""
        if self.email_destination_override:
            if isinstance(self.email_destination_override, str):
                return [self.email_destination_override]
            return self.email_destination_override

        # Use environment-based defaults
        if environment == "local":
            return []  # No emails in local environment
        elif environment == "dev":
            return ["<EMAIL>"]
        else:  # prod
            return ["<EMAIL>"]

    @property
    def slack_channel(self) -> str:
        """Get the appropriate Slack channel (requires environment context)."""
        # This property is kept for backward compatibility but requires environment context
        # Use get_slack_channel(environment) method instead
        return self.slack_channel_override or "#dev-airflow-notifications-prod"

    @property
    def email_destination(self) -> List[str]:
        """Get the appropriate email destination (requires environment context)."""
        # This property is kept for backward compatibility but requires environment context
        # Use get_email_destination(environment) method instead
        if self.email_destination_override:
            if isinstance(self.email_destination_override, str):
                return [self.email_destination_override]
            return self.email_destination_override
        return ["<EMAIL>"]  # Default to production
