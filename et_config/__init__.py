"""
Configuration package for ETDAG framework.

This package provides environment-aware configuration management
including environment definitions, notification settings, and team mappings.

Main exports:
- Configuration models (NotificationConfig, EnvironmentConfig, etc.)
- Environment registry functions
- Team utility functions
"""

from et_config.environments.registry import (
    get_current_environment,
    get_environment_config,
    validate_environment_config,
)
from et_config.models.environment import ConnectionConfig, EnvironmentConfig, S3Config
from et_config.models.notifications import NotificationConfig

# DAG-specific configuration models removed - now using registry with DAG-specific overrides
from et_config.teams.utils import (
    get_team_emails,
    get_team_slack_channel,
)

__all__ = [
    # Configuration models
    "NotificationConfig",
    "EnvironmentConfig",
    "S3Config",
    "ConnectionConfig",
    # Environment functions
    "get_current_environment",
    "get_environment_config",
    "validate_environment_config",
    # Team functions
    "get_team_emails",
    "get_team_slack_channel",
]
