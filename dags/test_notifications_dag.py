"""
Test DAG for ETDAG v2 Notification System

This DAG is designed to test all notification capabilities of the etdag_v2 framework:
- Slack notifications (success and failure)
- Email notifications (success and failure) 
- JSM Operations alerts (failure only)

The DAG includes tasks that will purposely fail to test the notification system.
"""

import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any

import pendulum
from airflow.decorators import task
from airflow.exceptions import AirflowException
from airflow.models.param import Param

from etdag_v2.decorators import etdag
from et_config import NotificationConfig

logger = logging.getLogger(__name__)


def _get_notification_config():
    """Get comprehensive notification configuration for testing all alert types."""
    return NotificationConfig(
        # Slack notifications
        slack_on_success=True,  # Test success notifications
        slack_on_failure=True,  # Test failure notifications
        slack_channel_override="#dev-airflow-notifications-dev",  # Override for testing
        
        # Email notifications
        email_on_success=True,  # Test email success notifications
        email_on_failure=True,  # Test email failure notifications
        email_destination_override=["<EMAIL>"],  # Test email override
        
        # JSM Operations alerts
        jsm_operations_alert=True,  # Test JSM Operations integration
        jsm_operations_priority="P1",  # Critical priority for testing
        team_owner="analytics-engineering",  # Test team-based routing
    )


@etdag(
    dag_id="test_notifications_dag",
    owner="analytics-engineering",
    description="Test DAG for comprehensive notification system testing",
    business_purpose="Testing all notification capabilities of etdag_v2 framework including Slack, email, and JSM Operations alerts",
    data_sources=["test-data"],
    downstream_systems=["notification-systems"],
    tags=["test", "notifications", "etdag_v2", "jsm-operations"],
    start_date=pendulum.today("UTC").add(days=-1),
    schedule=None,  # Manual trigger only
    catchup=False,
    max_active_tasks=1,
    max_active_runs=1,
    default_retries=0,  # No retries for testing
    notification_config=_get_notification_config(),
    params={
        "test_type": Param(
            default="failure",
            enum=["success", "failure", "mixed"],
            description="Type of test to run: success (all tasks succeed), failure (all tasks fail), mixed (some succeed, some fail)"
        ),
        "failure_message": Param(
            default="This is a test failure to verify JSM Operations integration",
            description="Custom failure message for testing"
        ),
        "delay_seconds": Param(
            default=5,
            description="Delay in seconds before task execution (for testing timing)"
        )
    },
    doc_md=__doc__,
)
def test_notifications_dag():
    """Test DAG for comprehensive notification system validation."""
    
    @task
    def start_test(test_type: str, **context) -> Dict[str, Any]:
        """Initialize the notification test."""
        logger.info(f"🧪 Starting notification test: {test_type}")
        logger.info(f"DAG Run ID: {context['dag_run'].run_id}")
        logger.info(f"Execution Date: {context['execution_date']}")
        
        return {
            "test_type": test_type,
            "start_time": str(pendulum.now()),
            "dag_run_id": context['dag_run'].run_id
        }
    
    @task
    def test_success_task(test_info: Dict[str, Any], delay_seconds: int, **context) -> Dict[str, Any]:
        """Task that always succeeds (for testing success notifications)."""
        import time
        
        logger.info(f"⏳ Waiting {delay_seconds} seconds before success...")
        time.sleep(delay_seconds)
        
        logger.info("✅ Success task completed successfully!")
        logger.info(f"Test Type: {test_info['test_type']}")
        
        return {
            "task": "success_task",
            "status": "completed",
            "message": "This task succeeded as expected for notification testing"
        }
    
    @task
    def test_failure_task(test_info: Dict[str, Any], failure_message: str, delay_seconds: int, **context) -> None:
        """Task that always fails (for testing failure notifications)."""
        import time
        
        logger.info(f"⏳ Waiting {delay_seconds} seconds before failure...")
        time.sleep(delay_seconds)
        
        logger.error("❌ About to trigger test failure for notification testing")
        logger.error(f"Test Type: {test_info['test_type']}")
        logger.error(f"Custom Message: {failure_message}")
        
        # Purposely fail to test notifications
        raise AirflowException(f"🚨 TEST FAILURE: {failure_message}")
    
    @task
    def test_conditional_task(test_info: Dict[str, Any], failure_message: str, **context) -> Dict[str, Any]:
        """Task that succeeds or fails based on test_type parameter."""
        test_type = test_info['test_type']
        
        if test_type == "success":
            logger.info("✅ Conditional task: SUCCESS mode")
            return {"status": "success", "mode": test_type}
        elif test_type == "failure":
            logger.error("❌ Conditional task: FAILURE mode")
            raise AirflowException(f"🚨 CONDITIONAL TEST FAILURE: {failure_message}")
        else:  # mixed
            logger.info("✅ Conditional task: MIXED mode (success)")
            return {"status": "success", "mode": test_type}
    
    @task
    def validate_notifications(test_info: Dict[str, Any], **context) -> Dict[str, Any]:
        """Final validation task to summarize test results."""
        logger.info("📊 Notification test validation:")
        logger.info(f"  - Test Type: {test_info['test_type']}")
        logger.info(f"  - Start Time: {test_info['start_time']}")
        logger.info(f"  - End Time: {str(pendulum.now())}")
        logger.info("  - Expected Notifications:")
        logger.info("    ✉️  Slack: Success and/or Failure alerts")
        logger.info("    📧 Email: Success and/or Failure notifications")
        logger.info("    🚨 JSM Operations: Failure alerts (P1 priority)")
        logger.info("    👥 Team: analytics-engineering")
        
        return {
            "test_completed": True,
            "test_type": test_info['test_type'],
            "validation_time": str(pendulum.now())
        }
    
    # Define task dependencies based on test type
    start = start_test()
    
    # Always run success task for baseline
    success = test_success_task(start)
    
    # Conditional tasks based on parameters
    conditional = test_conditional_task(start)
    
    # Always run failure task to test JSM Operations
    failure = test_failure_task(start)
    
    # Validation task (will only run if previous tasks succeed)
    validation = validate_notifications(start)
    
    # Set up dependencies
    start >> [success, conditional] >> validation
    start >> failure  # Failure task runs independently to ensure it fails


# Create the DAG instance
dag = test_notifications_dag
