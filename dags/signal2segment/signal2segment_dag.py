"""
Signal2Segment DAG - Modernized Implementation

## Business Purpose
Automates the creation and delivery of prospecting audiences based on configurable
signal-to-segment rules. This DAG processes audience configurations, validates them
through Lambda functions, generates audiences using PyGene API, and delivers them
to specified order lines for campaign targeting.

## Features
- **Environment-aware processing**: Adapts behavior based on environment (local/dev/prod)
- **Robust error handling**: Comprehensive validation and retry logic with Lambda validation
- **Performance monitoring**: Tracks execution times and success rates
- **Intelligent alerting**: Environment-specific notification routing via Slack
- **Configuration-driven**: Supports both manual triggers and S3-based config discovery

## Dependencies
- **Source System**: S3 configurations or manual API triggers
- **Target System**: PyGene Audience API and Campaign API for order line attachment
- **External Services**: AWS Lambda for query validation, Slack for notifications

## Environment Behavior
- **Local**: Uses mock/simplified processing with development credentials
- **Dev**: Uses development PyGene environment with reduced validation
- **Prod**: Full production processing with comprehensive validation and monitoring

## Data Flow
1. **Extract**: Retrieve configurations from DAG parameters or S3 discovery
2. **Validate**: Validate query arguments through Lambda function calls
3. **Generate**: Create prospecting audiences using PyGene Audience API
4. **Deliver**: Attach audiences to specified order lines via Campaign API
5. **Monitor**: Track success/failure and send appropriate notifications

Owner: data-engineering
Schedule: Manual trigger or cron-based (2am EDT daily)
SLA: 2 hours
"""

import logging
from datetime import timedelta
from typing import Dict, List, Any

import pendulum
from airflow.decorators import task, task_group
from airflow.exceptions import AirflowException, AirflowSkipException
from airflow.models.param import Param

from etdag_v2.decorators import etdag
from et_config import NotificationConfig

logger = logging.getLogger(__name__)


def _get_notification_config():
    """Get notification configuration for the DAG."""
    return NotificationConfig(
        email_on_success=False,
        email_on_failure=False,
        slack_on_success=False,
        slack_on_failure=True,
        team_owner="analytics-engineering",
        opsgenie_enabled=False,
    )


@task
def get_configurations(**context) -> List[Dict[str, Any]]:
    """
    Extract configurations from DAG run parameters.

    Returns:
        List of configuration dictionaries to process
    """
    logger.info("🔍 Extracting configurations from DAG run parameters")

    try:
        config = context["dag_run"].conf.get("config", [])

        if not config:
            logger.info("ℹ️ No configurations provided, skipping processing")
            raise AirflowSkipException("No configurations provided")

        logger.info(f"✅ Found {len(config)} configurations to process")
        return config

    except Exception as e:
        logger.error(f"❌ Failed to extract configurations: {e}")
        raise AirflowException(f"Configuration extraction failed: {e}")


@etdag(
    dag_id="signal2segment_modernized",
    owner="data-engineering",
    description="Modernized signal-to-segment audience generation and delivery system",
    business_purpose="Automate creation and delivery of prospecting audiences for campaign targeting",
    data_sources=["S3 configurations", "Manual API triggers", "PyGene Audience API"],
    tags=[
        "audience",
        "prospecting",
        "signal2segment",
    ],
    start_date=pendulum.today("UTC").add(days=-2),
    schedule=None,  # Manual trigger, can be changed to "@daily" for cron
    catchup=False,
    max_active_tasks=3,
    retry_delay=timedelta(minutes=5),
    sla_minutes=120,
    notification_config=_get_notification_config(),
    doc_md=__doc__,
    params={
        "config": Param(
            default=[],
            type="array",
            description="List of signal2segment configurations to process",
            schema={
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "org_id": {"type": "string", "description": "Organization ID"},
                        "audience_type": {
                            "type": "string",
                            "description": "Type of audience to generate",
                        },
                        "query_args": {
                            "type": "string",
                            "description": "Query arguments for audience generation",
                        },
                        "data_source": {
                            "type": "string",
                            "description": "Data source identifier",
                        },
                        "audience_name_prefix": {
                            "type": "string",
                            "description": "Prefix for generated audience names",
                        },
                        "automation": {
                            "type": "object",
                            "properties": {
                                "interval_value": {"type": "integer"},
                                "interval_unit": {"type": "string"},
                                "type": {"type": "string"},
                            },
                            "required": ["interval_value", "interval_unit", "type"],
                        },
                        "destinations": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "order_line_id": {"type": "string"},
                                    "is_exclude": {"type": "boolean"},
                                    "detach_previous_audience": {"type": "boolean"},
                                },
                                "required": ["order_line_id"],
                            },
                        },
                    },
                    "required": [
                        "org_id",
                        "audience_type",
                        "query_args",
                        "data_source",
                        "audience_name_prefix",
                        "automation",
                        "destinations",
                    ],
                },
            },
        ),
    },
)
def signal2segment_dag():
    """Define the modernized Signal2Segment DAG using TaskFlow API."""

    # Get configurations
    configurations = get_configurations()

    # Import task functions here to avoid top-level imports
    from dags.signal2segment.tasks.validation import validate_configuration
    from dags.signal2segment.tasks.audience_generation import generate_audience
    from dags.signal2segment.tasks.delivery import attach_audiences_to_order_lines

    # Use dynamic task mapping to process each configuration
    # The expand() method will create one task instance per item in the configurations list
    validated_configs = validate_configuration.expand(config=configurations)
    audience_results = generate_audience.expand(validated_config=validated_configs)
    attach_audiences_to_order_lines.expand(audience_result=audience_results)


# The @etdag decorator returns a DAG object, not a function
dag = signal2segment_dag
