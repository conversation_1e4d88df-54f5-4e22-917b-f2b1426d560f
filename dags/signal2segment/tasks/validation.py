"""
Validation tasks for Signal2Segment DAG.

This module contains tasks for validating configurations and query arguments
using AWS Lambda functions, following modern TaskFlow API patterns.
"""

import json
import logging
from datetime import timedelta
from typing import Dict, Any

from airflow.decorators import task
from airflow.exceptions import AirflowException
from airflow.providers.amazon.aws.operators.lambda_function import (
    LambdaInvokeFunctionOperator,
)

from dags.signal2segment.config import get_signal2segment_config

logger = logging.getLogger(__name__)


@task(
    retries=3,
    retry_delay=timedelta(minutes=1),
)
def validate_configuration(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate a single configuration using AWS Lambda function.

    This task validates query arguments by invoking the quoteprospect Lambda function
    and ensures the configuration is valid before proceeding with audience generation.

    Args:
        config: Configuration dictionary containing audience type, query args, etc.

    Returns:
        Validated configuration dictionary

    Raises:
        AirflowException: If validation fails or Lambda returns an error (allows retries)
    """
    logger.info(f"🔍 Validating configuration for org_id: {config.get('org_id')}")

    try:
        # Validate required fields
        required_fields = [
            "org_id",
            "audience_type",
            "query_args",
            "data_source",
            "audience_name_prefix",
            "destinations",
        ]

        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field: {field}")

        # Get signal2segment configuration
        signal2segment_config = get_signal2segment_config()
        query_map = signal2segment_config["query_map"]
        lambda_config = signal2segment_config["lambda_config"]

        audience_type = config["audience_type"]
        if audience_type not in query_map:
            available_types = ", ".join(query_map.keys())
            raise ValueError(
                f"Unknown audience_type '{audience_type}'. Available: {available_types}"
            )

        query_id = query_map[audience_type]
        logger.info(f"📋 Using query_id: {query_id} for audience_type: {audience_type}")

        # Prepare Lambda payload
        lambda_payload = {
            "query_id": query_id,
            "query_args": config["query_args"],
            "limit": lambda_config["validation_limit"],
        }

        logger.info(
            f"🚀 Invoking Lambda function for validation: {lambda_config['function_name']}"
        )

        # Invoke Lambda function for validation
        lambda_operator = LambdaInvokeFunctionOperator(
            task_id="lambda_validation_invoke",
            function_name=lambda_config["function_name"],
            invocation_type=lambda_config["invocation_type"],
            log_type=lambda_config["log_type"],
            aws_conn_id="aws_default",
            region_name=lambda_config["region_name"],
            payload=json.dumps(lambda_payload),
        )

        # Execute Lambda function
        lambda_response = lambda_operator.execute(context={})
        lambda_result = json.loads(lambda_response)

        logger.info(f"📊 Lambda response: {lambda_result}")

        # Check for errors in Lambda response
        if "error" in lambda_result:
            error_msg = lambda_result["error"]
            logger.error(f"❌ Lambda validation failed: {error_msg}")

            # Failure notifications are handled by etdag_v2 built-in messaging

            raise AirflowException(f"Configuration validation failed: {error_msg}")

        # Validation successful
        logger.info(
            f"✅ Configuration validation successful for org_id: {config['org_id']}"
        )

        # Add validation metadata to config
        validated_config = config.copy()
        validated_config["validation_metadata"] = {
            "query_id": query_id,
            "lambda_response": lambda_result,
            "validated_at": "{{ ts }}",
        }

        return validated_config

    except ValueError as e:
        logger.error(f"📊 Configuration validation error: {e}")
        # Failure notifications are handled by etdag_v2 built-in messaging
        raise AirflowException(f"Configuration validation failed: {e}")

    except Exception as e:
        logger.error(f"❌ Unexpected error during validation: {e}")
        raise AirflowException(f"Validation task failed: {e}")
