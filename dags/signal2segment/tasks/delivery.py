"""
Delivery tasks for Signal2Segment DAG.

This module contains tasks for attaching generated audiences to order lines
using the PyGene Campaign API, following modern TaskFlow API patterns.
"""

import logging
from datetime import timedelta
from typing import Dict, Any, List

from airflow.decorators import task  # needed in 2.10

# from airflow.sdk import task  # needed in 3.0
from airflow.exceptions import AirflowException
from airflow.hooks.base import BaseHook

logger = logging.getLogger(__name__)


@task(
    retries=3,
    retry_delay=timedelta(seconds=15),
)
def attach_audiences_to_order_lines(
    audience_result: Dict[str, Any], **context
) -> Dict[str, Any]:
    """
    Attach generated audiences to specified order lines.

    This task takes the generated audience and attaches it to all specified
    order lines in the configuration, handling existing audiences and
    detachment as needed.

    Args:
        audience_result: Result from audience generation task containing audience ID and config
        **context: Airflow context containing execution date and other metadata

    Returns:
        Dictionary containing delivery results and metadata

    Raises:
        AirflowException: If audience attachment fails (allows retries)
    """
    # Import PyGene modules inside task to avoid top-level imports
    from pygene.campaign_api import NextGenCampaignAPI
    from et_config import get_current_environment, get_environment_config

    config = audience_result["config"]
    audience_id = audience_result["audience_id"]

    logger.info(f"🚀 Starting audience delivery for org_id: {config['org_id']}")
    logger.info(f"📊 Audience ID: {audience_id}")

    try:
        # Get environment configuration and credentials
        env = get_current_environment()
        env_config = get_environment_config()
        credentials = BaseHook.get_connection(env_config.connections.pygene_conn_id)

        client_id = credentials.login
        client_secret = credentials.password

        # Initialize Campaign API
        campaign_api = NextGenCampaignAPI(
            client_id=client_id,
            client_secret=client_secret,
            org_id=config["org_id"],
            env=env,
        )

        delivery_results = []

        # Process each destination
        for destination in config["destinations"]:
            order_line_id = destination["order_line_id"]
            is_exclude = destination.get("is_exclude", False)
            detach_previous = destination.get("detach_previous_audience", False)

            logger.info(f"🎯 Processing order line: {order_line_id}")

            try:
                # Get existing audiences for this order line
                old_audience_ids = _get_existing_audience_ids(
                    campaign_api, order_line_id
                )

                # Check if audience is already attached
                if audience_id in old_audience_ids:
                    logger.info(
                        f"ℹ️ Audience {audience_id} already attached to order line {order_line_id}"
                    )
                    delivery_results.append(
                        {
                            "order_line_id": order_line_id,
                            "status": "skipped",
                            "reason": "audience_already_attached",
                            "audience_id": audience_id,
                        }
                    )
                    continue

                # Attach new audience
                logger.info(
                    f"📎 Attaching audience {audience_id} to order line {order_line_id}"
                )
                campaign_api.batch_add_audiences(
                    order_line_id, [audience_id], is_exclude
                )

                logger.info(
                    f"✅ Successfully attached audience to order line {order_line_id}"
                )

                # Handle previous audience detachment if requested
                if detach_previous and old_audience_ids:
                    logger.info(f"🔄 Detaching previous audiences: {old_audience_ids}")
                    campaign_api.batch_remove_audiences(order_line_id, old_audience_ids)
                    logger.info(
                        f"✅ Detached {len(old_audience_ids)} previous audiences"
                    )

                delivery_results.append(
                    {
                        "order_line_id": order_line_id,
                        "status": "success",
                        "audience_id": audience_id,
                        "is_exclude": is_exclude,
                        "detached_audiences": (
                            old_audience_ids if detach_previous else []
                        ),
                    }
                )

            except Exception as e:
                error_msg = str(e)
                logger.error(
                    f"❌ Failed to process order line {order_line_id}: {error_msg}"
                )

                # Handle specific error types - these should be retried as they're likely temporary
                if "504" in error_msg:
                    logger.error(f"🕐 Gateway timeout for order line {order_line_id}")
                    raise AirflowException(
                        f"Gateway timeout attaching audience to order line {order_line_id}"
                    )
                elif "500" in error_msg:
                    logger.error(
                        f"🔥 Internal server error for order line {order_line_id}"
                    )
                    raise AirflowException(
                        f"Internal server error attaching audience to order line {order_line_id}"
                    )
                else:
                    # Log error but continue with other order lines
                    logger.warning(
                        f"⚠️ Unexpected error for order line {order_line_id}: {error_msg}"
                    )
                    delivery_results.append(
                        {
                            "order_line_id": order_line_id,
                            "status": "failed",
                            "error": error_msg,
                            "audience_id": audience_id,
                        }
                    )

        # Check if any deliveries were successful
        successful_deliveries = [
            r for r in delivery_results if r["status"] == "success"
        ]
        failed_deliveries = [r for r in delivery_results if r["status"] == "failed"]

        logger.info(
            f"📊 Delivery summary: {len(successful_deliveries)} successful, {len(failed_deliveries)} failed"
        )

        # Success notifications are handled by etdag_v2 built-in messaging

        # Fail task if all deliveries failed - allow retries in case it was temporary
        if failed_deliveries and not successful_deliveries:
            raise AirflowException(
                f"All audience deliveries failed: {failed_deliveries}"
            )

        # Prepare final result
        result = {
            "audience_id": audience_id,
            "config": config,
            "delivery_results": delivery_results,
            "summary": {
                "total_destinations": len(config["destinations"]),
                "successful_deliveries": len(successful_deliveries),
                "failed_deliveries": len(failed_deliveries),
                "skipped_deliveries": len(
                    [r for r in delivery_results if r["status"] == "skipped"]
                ),
            },
            "delivered_at": "{{ ts }}",
        }

        return result

    except Exception as e:
        error_message = f"Audience delivery failed: {str(e)}"
        logger.error(f"❌ {error_message}")

        # Failure notifications are handled by etdag_v2 built-in messaging

        raise AirflowException(error_message)


def _get_existing_audience_ids(campaign_api, order_line_id: str) -> List[str]:
    """
    Get existing audience IDs for an order line.

    Args:
        campaign_api: PyGene Campaign API instance
        order_line_id: Order line ID to check

    Returns:
        List of existing audience IDs
    """
    try:
        order_line = campaign_api.get_order_line(order_line_id)
        audience_ids = [audience.id for audience in order_line.audiences]

        if audience_ids:
            logger.info(
                f"📋 Found {len(audience_ids)} existing audiences for order line {order_line_id}"
            )
        else:
            logger.info(
                f"📋 No existing audiences found for order line {order_line_id}"
            )

        return audience_ids

    except Exception as e:
        logger.warning(
            f"⚠️ Failed to get existing audiences for order line {order_line_id}: {e}"
        )
        return []
