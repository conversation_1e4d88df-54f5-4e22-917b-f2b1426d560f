"""
Simple JSM Operations Test DAG

This DAG is specifically designed to test JSM Operations integration.
It will purposely fail to trigger JSM Operations alerts for testing.
"""

import logging
from datetime import timedelta

import pendulum
from airflow.decorators import task
from airflow.exceptions import AirflowException
from airflow.models.param import Param

from etdag_v2.decorators import etdag
from et_config import NotificationConfig

logger = logging.getLogger(__name__)


def _get_jsm_test_notification_config():
    """Get JSM Operations focused notification configuration."""
    return NotificationConfig(
        # Minimal Slack for comparison
        slack_on_failure=True,
        slack_channel_override="#dev-airflow-notifications-dev",
        # JSM Operations - the main focus
        jsm_operations_alert=True,
        jsm_operations_priority="P1",  # Critical for testing
        team_owner="analytics-engineering",  # Test team routing
        # Disable other notifications to focus on JSM
        email_on_success=False,
        email_on_failure=False,
        slack_on_success=False,
    )


@etdag(
    dag_id="test_jsm_operations_dag",
    owner="analytics-engineering",
    description="Simple test DAG specifically for JSM Operations alert testing",
    business_purpose="Testing JSM Operations integration and team-based routing",
    tags=["test", "jsm-operations", "critical"],
    start_date=pendulum.today("UTC").add(days=-1),
    schedule=None,  # Manual trigger only
    catchup=False,
    max_active_tasks=1,
    max_active_runs=1,
    default_retries=0,  # No retries - we want immediate failure
    notification_config=_get_jsm_test_notification_config(),
    params={
        "team_to_test": Param(
            default="analytics-engineering",
            enum=[
                "analytics-engineering",
                "business-intelligence",
                "data-engineering",
                "platform-team",
            ],
            description="Team to test JSM Operations routing for",
        ),
        "priority_level": Param(
            default="P1",
            enum=["P1", "P2", "P3", "P4", "P5"],
            description="JSM Operations priority level to test",
        ),
        "failure_delay": Param(
            default=10,
            description="Seconds to wait before triggering failure (allows time to monitor)",
        ),
    },
    doc_md=__doc__,
)
def test_jsm_operations_dag():
    """Simple JSM Operations test DAG."""

    @task
    def jsm_test_setup(team_to_test: str, priority_level: str, **context) -> dict:
        """Setup task that logs test configuration and environment details."""
        from et_config import get_current_environment, get_environment_config
        from etdag_v2.utils import is_production
        import os

        # Get environment information
        current_env = get_current_environment()
        env_config = get_environment_config()
        is_prod = is_production()

        logger.info("🚨 JSM Operations Test Setup")
        logger.info("🔍 Environment Detection:")
        logger.info(f"  Current Environment: {current_env}")
        logger.info(f"  Is Production: {is_prod}")
        logger.info(f"  Environment Config Name: {env_config.name}")
        logger.info(f"  Environment Config is_production: {env_config.is_production}")
        logger.info(f"  Environment Config is_prod: {env_config.is_prod}")
        logger.info("🔍 Environment Variables:")
        logger.info(f"  ENVIRONMENT: {os.getenv('ENVIRONMENT', 'NOT_SET')}")
        logger.info(
            f"  AIRFLOW_ENVIRONMENT: {os.getenv('AIRFLOW_ENVIRONMENT', 'NOT_SET')}"
        )
        logger.info(f"  ENV: {os.getenv('ENV', 'NOT_SET')}")
        logger.info(f"  DEPLOYMENT_ENV: {os.getenv('DEPLOYMENT_ENV', 'NOT_SET')}")

        # Try to get Airflow Variable
        try:
            from airflow.models import Variable

            airflow_env = Variable.get("environment", default_var="NOT_SET")
            logger.info(f"  Airflow Variable 'environment': {airflow_env}")
        except Exception as e:
            logger.info(f"  Airflow Variable 'environment': ERROR - {str(e)}")

        logger.info("📋 Test Configuration:")
        logger.info(f"  Team to test: {team_to_test}")
        logger.info(f"  Priority level: {priority_level}")
        logger.info(f"  DAG Run ID: {context['dag_run'].run_id}")
        logger.info(f"  Execution Date: {context['execution_date']}")

        if is_prod:
            logger.info(
                "✅ JSM Operations alerts WILL be sent (production environment detected)"
            )
        else:
            logger.warning(
                "⚠️  JSM Operations alerts will NOT be sent (non-production environment)"
            )
            logger.warning(
                "   To test JSM Operations, ensure environment is set to 'prod'"
            )

        logger.info("  Expected JSM Alert Details (if production):")
        logger.info(f"    - Team: {team_to_test}")
        logger.info(f"    - Priority: {priority_level}")
        logger.info("    - Alert Type: DAG Failure")
        logger.info("    - Source: Airflow")

        return {
            "team": team_to_test,
            "priority": priority_level,
            "test_start": str(pendulum.now()),
            "environment": current_env,
            "is_production": is_prod,
        }

    @task
    def trigger_jsm_failure(test_config: dict, failure_delay: int, **context) -> None:
        """Task that purposely fails to trigger JSM Operations alert."""
        import time

        logger.info(
            f"⏳ Waiting {failure_delay} seconds before triggering JSM Operations test failure..."
        )
        logger.info("   (This gives you time to monitor JSM Operations dashboard)")

        time.sleep(failure_delay)

        logger.error("🚨 TRIGGERING JSM OPERATIONS TEST FAILURE")
        logger.error(f"Team: {test_config['team']}")
        logger.error(f"Priority: {test_config['priority']}")
        logger.error(f"Environment: {test_config['environment']}")
        logger.error(f"Is Production: {test_config['is_production']}")
        logger.error(f"Test Start: {test_config['test_start']}")
        logger.error(f"Failure Time: {str(pendulum.now())}")

        if test_config["is_production"]:
            logger.error("✅ JSM Operations alert SHOULD be triggered for this failure")
        else:
            logger.error(
                "⚠️  JSM Operations alert will NOT be triggered (non-production environment)"
            )

        # This failure should trigger JSM Operations alert (if in production)
        raise AirflowException(
            f"🚨 JSM OPERATIONS TEST FAILURE - Team: {test_config['team']}, "
            f"Priority: {test_config['priority']}, Environment: {test_config['environment']}, "
            f"Production: {test_config['is_production']} - "
            f"This is a deliberate failure to test JSM Operations integration. "
            f"Expected alert should be sent to {test_config['team']} team in JSM Operations "
            f"{'(if environment is production)' if not test_config['is_production'] else ''}."
        )

    # Simple linear flow
    setup = jsm_test_setup()
    failure = trigger_jsm_failure(setup)

    setup >> failure


# Create the DAG instance
dag = test_jsm_operations_dag
