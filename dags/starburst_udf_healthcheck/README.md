# Starburst UDF Health Check DAG

## Overview

The Starburst UDF Health Check DAG (`starburst_udf_healthcheck`) is a critical monitoring DAG that validates the availability and functionality of essential User Defined Functions (UDFs) in the Starburst/Trino environment.

## Business Purpose

This DAG ensures that critical UDFs required for data processing pipelines are available and functioning correctly. It provides early detection of UDF issues that could impact downstream data processing workflows.

## Features

### 🔍 Environment-Aware Testing
- Automatically detects which UDFs are supported in the current environment
- Gracefully skips unsupported UDFs in development/local environments
- Provides full testing coverage in production environments

### 📊 Comprehensive UDF Coverage
- **ethash_v1/v2**: Address hashing functions with both parts and string inputs
- **geocodeAddress**: Geocoding capabilities with real address samples
- Extensible framework for adding new UDF tests

### 🚨 Intelligent Alerting
- Environment-specific Slack notifications
- JSM integration for production failures
- Detailed error reporting with context
- Performance threshold monitoring

### ⚡ Performance Monitoring
- Query execution time tracking
- Performance threshold validation
- Trend analysis capabilities
- Resource usage monitoring

## Schedule & Execution

- **Schedule**: Every 2 hours (`0 */2 * * *`)
- **SLA**: 2 hours (should complete within SLA for timely alerting)
- **Catchup**: Disabled
- **Max Active Runs**: 1
- **Max Active Tasks**: 3

## Environment Behavior

### Local Environment
- UDFs typically unavailable
- Tests gracefully skipped with informational logging
- No critical alerts generated

### Development Environment
- Limited UDF availability
- Tests run for available UDFs only
- Alerts sent to development channels

### Production Environment
- Full UDF testing suite
- Comprehensive monitoring and alerting
- JSM integration for critical failures
- Performance threshold enforcement

## UDF Test Configuration

### ethash_v1 & ethash_v2
```sql
-- Test with address parts
SELECT ethash_v1('522', 'Market Street', '40202', '', '', 'E', '', '') as hash_result

-- Test with address string
SELECT ethash_v1('522 E. Market Street, 40202') as hash_result
```

### geocodeAddress
```sql
-- Test with Vancouver address
SELECT geocodeAddress('6609 NE 71st Ave, Vancouver, WA', ARRAY['etHash','geometryWKB']) as geocode_result

-- Test with Louisville address
SELECT geocodeAddress('522 E. Market Street, Louisville, KY 40202', ARRAY['etHash','geometryWKB']) as geocode_result
```

## Performance Thresholds

| UDF | Warning Threshold | Max Threshold | Timeout |
|-----|------------------|---------------|---------|
| ethash_v1 | 2 seconds | 5 seconds | 30 seconds |
| ethash_v2 | 2 seconds | 5 seconds | 30 seconds |
| geocodeAddress | 10 seconds | 15 seconds | 60 seconds |

## Task Flow

```mermaid
graph TD
    A[get_environment_info] --> B[udf_availability_checks]
    B --> C[test_ethash_v1_parts]
    B --> D[test_ethash_v1_string]
    B --> E[test_ethash_v2_parts]
    B --> F[test_ethash_v2_string]
    B --> G[test_geocoder_vancouver]
    B --> H[test_geocoder_louisville]
    C --> I[validate_udf_availability]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
```

## Monitoring & Alerting

### Success Notifications
- Disabled by default (configurable)
- Can be enabled for critical environments

### Failure Notifications
- **Slack**: Environment-specific channels
  - Local: `#dev-airflow-notifications-local`
  - Dev: `#dev-airflow-notifications-dev`
  - Prod: `#dev-airflow-notifications-prod`
- **JSM**: Production only, routed to platform team
- **Email**: Configurable, disabled by default

### Alert Escalation
1. **Level 1**: Slack notification to team channel
2. **Level 2**: JSM alert to platform team (production only)
3. **Level 3**: Email escalation (if configured)

## Troubleshooting

### Common Issues

#### UDF Not Found Error
```
ERROR: Function 'ethash_v1' not found
```
**Resolution**: 
1. Verify UDF is installed in Starburst cluster
2. Check environment configuration in `config/environments.py`
3. Ensure UDF libraries are properly deployed

#### Timeout Errors
```
ERROR: Query timeout after 30 seconds
```
**Resolution**:
1. Check Starburst cluster performance
2. Review query complexity
3. Adjust timeout thresholds if needed

#### Connection Errors
```
ERROR: Unable to connect to Starburst
```
**Resolution**:
1. Verify Starburst connection configuration
2. Check network connectivity
3. Validate connection credentials

### Performance Issues

#### Slow Query Execution
- Check Starburst cluster resource utilization
- Review query execution plans
- Consider UDF optimization

#### High Memory Usage
- Monitor Starburst worker memory
- Check for memory leaks in UDF implementations
- Scale cluster resources if needed

## Configuration

### Adding New UDFs

1. **Update UDF_TEST_MAPPING** in the DAG:
```python
"new_udf": [
    {
        "task_id": "test_new_udf_basic",
        "description": "Test new UDF basic functionality",
        "sql": "SELECT new_udf('test_input') as result",
        "expected_result_type": "string",
        "timeout_seconds": 30,
        "critical": True,
    }
]
```

2. **Update Performance Thresholds**:
```python
UDF_PERFORMANCE_THRESHOLDS = {
    "new_udf": {"max_duration_seconds": 10, "warning_duration_seconds": 5},
}
```

3. **Update Environment Configuration**:
```python
# In config/environments.py
supported_udfs = {
    "new_udf": True,  # Set per environment
}
```

### Modifying Notification Settings

```python
notification_config = NotificationConfig(
    slack_on_failure=True,
    slack_channel_override="#custom-channel",
    jsm_operations_alert=True,
    jsm_operations_priority="P2",
    team_owner="platform-team"
)
```

## Testing

### Unit Tests
Located in `tests/unit/test_starburst_udf_healthcheck.py`

Run tests:
```bash
pytest tests/unit/test_starburst_udf_healthcheck.py -v
```

### Integration Tests
```bash
# Run DAG validation
python -m pytest tests/integration/ -k starburst_udf
```

## Dependencies

- **Starburst/Trino cluster**: Target database for UDF testing
- **UDF libraries**: ethash, geocoding libraries
- **Network connectivity**: Access to Starburst cluster
- **Airflow connections**: Properly configured Starburst connections

## Maintenance

### Regular Tasks
- Review performance metrics monthly
- Update test queries as UDFs evolve
- Validate environment configurations quarterly
- Review and update performance thresholds

### Monitoring
- Track DAG success rate
- Monitor query execution times
- Review alert frequency and accuracy
- Analyze UDF usage patterns

## Support

### Team Ownership
- **Primary**: platform-team
- **Secondary**: data-engineering

### Escalation Path
1. Platform team Slack channel
2. JSM alerts (production)
3. Data engineering team
4. Infrastructure team (for cluster issues)

### Documentation Updates
This documentation should be updated when:
- New UDFs are added
- Performance thresholds change
- Environment configurations change
- Alert routing changes