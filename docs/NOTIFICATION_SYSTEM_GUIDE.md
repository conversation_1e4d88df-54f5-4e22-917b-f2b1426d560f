# ETDAG Notification System

## Overview

The ETDAG notification system provides configurable alerting capabilities across different environments, with support for Slack, email, and JSM Operations alerts.

## Features

### 1. Notification Configuration

The `NotificationConfig` class in `config/environments.py` serves as the central configuration point for all notification types in the ETDAG system.

#### Available Configuration Parameters

```python
NotificationConfig(
    # Slack configuration
    slack_on_success: bool = False,        # Send Slack notifications on success
    slack_on_failure: bool = True,         # Send Slack notifications on failure
    slack_channel_override: str = None,    # Override default Slack channel
    
    # Email configuration
    email_on_success: bool = False,        # Send email notifications on success
    email_on_failure: bool = False,        # Send email notifications on failure
    email_destination_override: Union[str, List[str]] = None,  # Override default email destinations
    
    # JSM Operations configuration
    jsm_operations_alert: bool = False,    # Enable JSM Operations alerts (production only)
    jsm_operations_priority: str = "P3",   # Priority level (P1=Critical, P2=High, P3=Medium, P4=Low, P5=Lowest)
    team_owner: str = None                 # Team owner for routing
)
```

### 2. Slack Notifications

- Default configuration: Slack notifications are enabled by default for failures (`slack_on_failure=True`) but disabled for successes (`slack_on_success=False`).
- Environment-specific channels: The system uses environment-specific default channels based on the current environment.
- Simple override: You can specify a custom Slack channel using `slack_channel_override`.

### 3. Email Notifications

- By default, email notifications are disabled (`email_on_success=False`, `email_on_failure=False`)
- Environment-specific defaults: Each environment has a default email destination.
- Simple override: You can specify custom email addresses using `email_destination_override`.

### 4. JSM Operations Integration

- JSM Operations integration for production-only alerts with priority-based escalation.
- JSM Operations alerts are only sent in production environments.
- Priority-based routing: Alerts are sent with configurable priority levels (P1=Critical to P5=Lowest).
- Team-based routing: All alerts are routed to the Analytics Engineering Guild team in JSM Operations.

### 5. Environment-Based Defaults

- Environment-specific Slack channels based on current environment (local, dev, prod)
- Environment-specific email destinations based on current environment
- Priority-based JSM Operations alerts with configurable escalation levels

## Usage Examples

### Basic DAG with Default Notifications

```python
from etdag import ETDAG

with ETDAG(
    dag_id="my_dag",
    owner="data-engineering",
    description="My DAG description",
    tags=["my", "tags"],
    # No explicit notification config - uses defaults
):
    # DAG tasks
```

### Custom Notification Configuration

```python
from config.environments import NotificationConfig
from etdag import ETDAG

notification_config = NotificationConfig(
    slack_on_success=True,  # Override default (False)
    slack_on_failure=True,  # Keep default (True)
    slack_channel_override="#my-custom-channel",  # Direct channel override
    email_on_failure=True,  # Override default (False)
    email_destination_override=["<EMAIL>"],  # Direct email override
    jsm_operations_alert=True,  # Will only be active in production
    jsm_operations_priority="P2",  # High priority alerts
    team_owner="analytics-engineering"  # For team-based routing
)

with ETDAG(
    dag_id="my_dag",
    owner="analytics-engineering",
    description="My DAG description",
    tags=["my", "tags"],
    notification_config=notification_config,
):
    # DAG tasks
```

## Advanced Configuration

### Environment-Specific Settings

The notification system adapts automatically based on the current environment:

```python
# In development environments:
- Slack alerts use #dev-airflow-notifications-dev channel by default
- Email notifications <NAME_EMAIL> if enabled
- JSM Operations alerts are disabled

# In production environments:
- Slack alerts use #dev-airflow-notifications-prod channel by default
- Email notifications <NAME_EMAIL> if enabled
- JSM Operations alerts are enabled if configured
```

### Available Properties

The NotificationConfig class provides the following properties that can be used in your code:

```python
# Get the appropriate Slack channel (either override or default)
notification_config.slack_channel

# Get the default Slack channel based on environment
notification_config.default_slack_channel

# Get the email destination list (either override or default)
notification_config.email_destination
```

### JSM Operations Priority Guidelines

JSM Operations alerts support priority-based escalation. Choose the appropriate priority level:

| Priority | When to Use | Examples |
|----------|-------------|----------|
| **P1 - Critical** | Business-critical DAGs requiring immediate attention | Toyota campaigns, revenue-impacting processes |
| **P2 - High** | Important DAGs needing prompt attention | Data pipeline failures, customer-facing features |
| **P3 - Medium** | Standard DAGs with moderate impact | Regular reporting, non-critical data processing |
| **P4 - Low** | Minor issues that can wait | Development/testing DAGs, nice-to-have reports |
| **P5 - Lowest** | Informational alerts | Maintenance notifications, low-priority tasks |

### JSM Operations Configuration

JSM Operations alerts are configured through Airflow connections:
- **Connection ID**: `jsm_operations_conn`
- **Authentication**: Basic Auth with API token (email in login, API token in password)
- **Extra**: Cloud ID and team mappings

**Connection Extra Configuration Example:**
```json
{
  "cloud_id": "3d546da2-b006-47ee-a72f-4b8b244c0770",
  "analytics-engineering": "422f5a8a-eafc-4a1d-8677-b2fd4b25af95",
  "business-intelligence": "34535325345345345435453",
  "data-engineering": "another-team-id-here",
  "platform-team": "yet-another-team-id"
}
```

The system will automatically route alerts to the appropriate JSM team based on the `team_owner` specified in the `NotificationConfig`.
```

### Modifying Environment Defaults

To change environment default settings for email or Slack, modify the properties in NotificationConfig:

```python
# Customize default Slack channels
@property
def default_slack_channel(self) -> str:
    env = get_current_environment()
    if env == "local":
        return "#dev-airflow-notifications-local"
    elif env == "dev":
        return "#dev-airflow-notifications-dev"
    else:
        return "#dev-airflow-notifications-prod"

# Customize default email destinations
@property
def email_destination(self) -> List[str]:
    # Check for override first...
    if self.email_destination_override:
        # ...existing code...
    
    # Environment-based defaults
    env = get_current_environment()
    if env == "local":
        return []  # No emails in local environment
    elif env == "dev":
        return ["<EMAIL>"]
    else:  # prod
        return ["<EMAIL>"]
```