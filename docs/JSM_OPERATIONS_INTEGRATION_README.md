# JSM Operations Integration for Airflow DAGs

## 🚨 Overview

This document describes the integration between our Airflow DAGs and Atlassian Jira Service Management (JSM) Operations for critical failure alerting. This system provides real-time, priority-based alerts to our Analytics Engineering Guild team when critical DAGs fail in production.

## 🎯 What is JSM Operations?

JSM Operations is Atlassian's incident management and alerting platform (formerly Opsgenie). It provides:

- **Real-time alerting** for critical system failures
- **Priority-based notifications** (P1=Critical to P5=Lowest)
- **Team-based routing** and escalation
- **Mobile app notifications** with customizable sounds
- **Integration with Slack, email, SMS, and voice calls**
- **On-call scheduling** and rotation management

## 🏗️ How Our Integration Works

### Architecture
```
Airflow DAG Failure → ETDAG Callback → JSM Operations API → Team Alerts → Individual Notifications
```

### Key Components
1. **ETDAG Class**: Enhanced with JSM Operations alerting capabilities
2. **Environment Detection**: Only sends alerts in production
3. **Priority Levels**: P1 (Critical) to P5 (Lowest) based on DAG importance
4. **Team Assignment**: All alerts route to Analytics Engineering Guild
5. **Secure Configuration**: Credentials stored in Airflow connections

## 🚀 Quick Start for DAG Developers

### Adding JSM Operations Alerts to Your DAG

```python
from etdag import ETDAG

with ETDAG(
    dag_id="my_critical_dag",
    # ... other DAG parameters ...
    et_failure_msg=True,  # Keep existing Slack alerts
    et_operations_alert=True,  # Enable JSM Operations alerts
    et_operations_priority="P1",  # Set priority level
) as dag:
    # Your DAG tasks here
```

### Priority Guidelines

| Priority | When to Use | Examples |
|----------|-------------|----------|
| **P1 - Critical** | Business-critical DAGs that require immediate attention | Toyota campaigns, revenue-impacting processes |
| **P2 - High** | Important DAGs that need prompt attention | Data pipeline failures, customer-facing features |
| **P3 - Medium** | Standard DAGs with moderate impact | Regular reporting, non-critical data processing |
| **P4 - Low** | Minor issues that can wait | Development/testing DAGs, nice-to-have reports |
| **P5 - Lowest** | Informational alerts | Maintenance notifications, low-priority tasks |

### Environment Behavior
- ✅ **Production**: Full JSM Operations alerting enabled
- ❌ **Development/Staging**: JSM Operations alerts disabled (Slack alerts still work)

## 👥 Team Configuration

### Analytics Engineering Guild Team
- **Team ID**: `************************b2fd4b25af95`
- **Team Members**: All Analytics Engineering Guild members
- **Escalation**: Automatic escalation after 15 minutes if unacknowledged
- **Coverage**: 24/7 monitoring with on-call rotation

### Current Team Members
- Rorie Lizenby (Team Lead)
- [Add other team members as they're added to JSM Operations]

## 📱 Personal Notification Setup

### Accessing Your Settings
1. Go to [Jira Service Management](https://eltorocorp.atlassian.net/)
2. Click your profile → **Personal Jira settings**
3. Select **Alert notifications** tab

### Mobile App Setup
1. **Download**: Install "Jira Cloud" app from App Store/Google Play
2. **Login**: Use your `@eltoro.com` credentials
3. **Enable Push Notifications**: 
   - iOS: Settings → Notifications → Jira Cloud → Allow Notifications
   - Android: App Settings → Notifications → Enable

### Notification Methods Available

#### 📧 Email
- **Default**: Enabled for all team members
- **Customization**: Set quiet hours, priority filtering
- **Delivery**: Immediate for P1/P2, batched for P3-P5

#### 📱 Push Notifications
- **Mobile App**: Real-time push notifications
- **Priority Sounds**: Different sounds for different priorities
- **Customization**: Enable/disable by priority level

#### 📞 SMS & Voice
- **Setup**: Add phone number in contact methods
- **Usage**: Typically for P1 alerts or escalation
- **Configuration**: Set up notification rules for specific priorities

#### 🔔 Custom Notification Rules
Create rules like:
- "Send SMS for P1 alerts immediately"
- "Send email for P2-P3 alerts during business hours"
- "No notifications for P4-P5 alerts on weekends"

### Priority-Based Notification Settings

#### Recommended Setup for Team Members:
```
P1 (Critical):
- Immediate push notification with loud sound
- SMS backup after 5 minutes if unacknowledged
- Email notification

P2 (High):
- Push notification with standard sound
- Email notification
- SMS during business hours only

P3 (Medium):
- Push notification (quiet sound)
- Email notification
- No SMS

P4-P5 (Low):
- Email only
- Batched notifications (every 30 minutes)
```

## 🔄 Default Escalation Path

### Automatic Escalation Timeline
1. **0 minutes**: Alert sent to all team members
2. **15 minutes**: If unacknowledged, escalate to team lead
3. **30 minutes**: If still unacknowledged, escalate to management
4. **45 minutes**: Final escalation to on-call engineer

### Manual Escalation
Team members can manually escalate alerts by:
- Clicking "Escalate" in the mobile app
- Replying to alert email with "escalate"
- Using Slack integration commands

## 📊 JSM Operations Dashboard

### Accessing the Dashboard
- **URL**: https://eltorocorp.atlassian.net/jira/ops/teams/************************b2fd4b25af95/alerts
- **Mobile**: Use Jira Cloud mobile app → Operations section

### Key Features
- **Active Alerts**: Current unresolved alerts
- **Alert History**: Past 30 days of alerts
- **Team Performance**: Response times and resolution metrics
- **On-Call Schedule**: Current and upcoming on-call assignments

### Alert Actions
- **Acknowledge**: "I'm working on this"
- **Resolve**: "Issue is fixed"
- **Escalate**: "Need additional help"
- **Add Note**: Provide status updates
- **Snooze**: Temporarily suppress notifications

## 🎯 Response Expectations

### Acknowledgment Times
- **P1 (Critical)**: 5 minutes
- **P2 (High)**: 15 minutes  
- **P3 (Medium)**: 30 minutes
- **P4-P5 (Low)**: 1 hour

### Resolution Times
- **P1 (Critical)**: 1 hour
- **P2 (High)**: 4 hours
- **P3 (Medium)**: 1 business day
- **P4-P5 (Low)**: 3 business days

### Communication Requirements
- **Acknowledge** alerts within target time
- **Provide updates** every 30 minutes for P1/P2 alerts
- **Document resolution** in alert notes
- **Conduct post-mortems** for P1 incidents

## 🔧 Technical Details

### Alert Payload Structure
```json
{
  "message": "🚨 Airflow DAG Failure: toyota_intender_west_7",
  "description": "DAG: toyota_intender_west_7\nTask: quote_file_prep\nExecution Date: 2025-07-10 05:30:42\nError: Connection timeout\n\nDAG URL: https://airflow.k8s.eltoro.com/dags/toyota_intender_west_7/grid",
  "priority": "P1",
  "source": "Airflow",
  "tags": ["airflow", "critical", "dag-failure"],
  "responders": [{"id": "************************b2fd4b25af95", "type": "team"}]
}
```

### Connection Configuration
Stored securely in Airflow connection `jsm_operations_conn`:
- **Host**: `api.atlassian.com`
- **Authentication**: Basic Auth with API token
- **Extra**: Cloud ID and Team ID

## 📚 Additional Resources

### Documentation
- [JSM Operations User Guide](https://support.atlassian.com/************************cloud/docs/discover-alerting-and-on-call/)
- [Mobile App Setup](https://support.atlassian.com/************************cloud/docs/alert-notifications/)
- [Notification Configuration](https://support.atlassian.com/************************cloud/docs/set-up-notification-preferences/)

### Support Contacts
- **Technical Issues**: Analytics Engineering Guild Slack channel
- **JSM Operations Setup**: Rorie Lizenby
- **Escalation**: Team Lead or Manager

## 🚨 Emergency Procedures

### If JSM Operations is Down
1. **Slack alerts** continue to work independently
2. **Manual notification** via team Slack channel
3. **Email backup** to team distribution list
4. **Escalate** to IT Operations for JSM status

### If You're Not Receiving Alerts
1. **Check** your notification settings in JSM Operations
2. **Verify** mobile app permissions
3. **Test** with a manual alert
4. **Contact** team lead for troubleshooting

---

**Last Updated**: July 2025  
**Maintained By**: Analytics Engineering Guild  
**Version**: 1.0
