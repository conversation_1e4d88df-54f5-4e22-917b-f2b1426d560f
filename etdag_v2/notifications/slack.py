"""
Slack notification functionality for ETDAG framework.

This module provides Slack notification capabilities including
success and failure alerts with environment-aware routing.
"""

import logging
from typing import Any, Dict, List, Optional

from airflow.providers.slack.operators.slack_webhook import SlackWebhookOperator

from et_config.environments import get_current_environment, get_environment_config
from etdag_v2.notifications.base import (
    BaseNotificationHandler,
    get_alert_emoji,
    get_status_text,
)
from etdag_v2.utils import is_production


class SlackNotificationHandler(BaseNotificationHandler):
    """Handler for Slack notifications."""

    def __init__(
        self, channel: Optional[str] = None, environment: Optional[str] = None
    ):
        """Initialize Slack notification handler.

        Args:
            channel: Slack channel override
            environment: Target environment
        """
        super().__init__(environment)
        self.channel = channel

    def send_success_notification(self, context: Dict[str, Any]) -> Optional[Any]:
        """Send Slack success notification."""
        return self._send_slack_alert(context, is_success=True)

    def send_failure_notification(self, context: Dict[str, Any]) -> Optional[Any]:
        """Send Slack failure notification."""
        return self._send_slack_alert(context, is_success=False)

    def _send_slack_alert(
        self, context: Dict[str, Any], is_success: bool = True
    ) -> Optional[Any]:
        """Send Slack alert with enhanced formatting.

        Args:
            context: Airflow context dictionary
            is_success: Whether this is a success or failure alert

        Returns:
            Slack webhook response or None if failed
        """
        try:
            context_info = self._extract_context_info(context)

            # Create Slack message blocks
            slack_msg = self._build_slack_message(context_info, is_success)

            # Get the correct Slack connection ID from environment config
            env_config = get_environment_config(self.environment)
            slack_conn_id = env_config.connections.slack_conn_id

            # Send the alert
            alert_op = SlackWebhookOperator(
                task_id="slack_alert",
                slack_webhook_conn_id=slack_conn_id,
                blocks=slack_msg,
                channel=self.channel,
            )

            return alert_op.execute(context)

        except Exception as e:
            self._handle_notification_error(e, "slack")
            return None

    def _build_slack_message(
        self, context_info: Dict[str, Any], is_success: bool
    ) -> List[Dict[str, Any]]:
        """Build Slack message blocks.

        Args:
            context_info: Extracted context information
            is_success: Whether this is a success or failure alert

        Returns:
            List of Slack message blocks
        """
        status_emoji = get_alert_emoji(is_success, is_production())
        status_text = get_status_text(is_success)

        # Header block
        blocks = [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f"{status_emoji} {status_text}: {context_info['dag_id']}",
                    "emoji": True,
                },
            }
        ]

        # Context information block
        fields = [
            {"type": "mrkdwn", "text": f"*Environment*: {context_info['environment']}"},
            {"type": "mrkdwn", "text": f"*Task*: {context_info['task_id']}"},
            {
                "type": "mrkdwn",
                "text": f"*Execution Time*: {context_info['execution_date']}",
            },
        ]

        blocks.append({"type": "section", "fields": fields})

        # Add error details for failures
        if not is_success:
            error_msg = str(
                context_info.get("exception")
                or context_info.get("reason", "Unknown error")
            )
            blocks.append(
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Error*: ```{error_msg[:500]}```",  # Truncate long errors
                    },
                }
            )

        # Add DAG link
        blocks.append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*DAG URL*: https://airflow.k8s.eltoro.com/dags/{context_info['dag_id']}/grid",
                },
            }
        )

        # Add runbook link
        blocks.append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Airflow Runbook*: https://eltorocorp.atlassian.net/wiki/spaces/DSG/pages/2001076256/Airflow",
                },
            }
        )

        return blocks


def task_slack_success_alert(context):
    """Sends a success message to Slack.

    Args:
        context: Airflow will pass in the context of the dag

    Returns:
        Slack SDK WebhookResponse
    """
    from etdag_v2.utils import log_success

    log_success(context)

    handler = SlackNotificationHandler()
    return handler.send_success_notification(context)


def task_slack_error_alert(context):
    """Sends an error message to Slack.

    Args:
        context: Airflow will pass in the context of the dag

    Returns:
        Slack SDK WebhookResponse
    """
    from etdag_v2.utils import log_failure

    log_failure(context)

    handler = SlackNotificationHandler()
    return handler.send_failure_notification(context)


# Legacy function names for backward compatibility during transition
def task_slack_success_alert_legacy(context):
    """Legacy function - use task_slack_success_alert instead."""
    return task_slack_success_alert(context)


def task_slack_error_alert_legacy(context):
    """Legacy function - use task_slack_error_alert instead."""
    return task_slack_error_alert(context)


# Import log functions for backward compatibility
from etdag_v2.utils import log_failure, log_success
