"""
Notification system for ETDAG framework.

This package provides modular notification capabilities including:
- Slack notifications
- Email notifications
- JSM Operations integration
- Base notification classes and utilities

Main exports:
- Slack notification functions
- Email notification functions
- JSM Operations integration functions
- Base notification classes
"""

from .base import BaseNotificationHandler
from .jsm import send_jsm_operations_alert
from .slack import task_slack_error_alert, task_slack_success_alert

__all__ = [
    "task_slack_success_alert",
    "task_slack_error_alert",
    "send_jsm_operations_alert",
    "BaseNotificationHandler",
]
