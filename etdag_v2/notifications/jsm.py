"""
JSM Operations notification functionality for ETDAG framework.

This module provides JSM Operations integration for critical production alerts
with team-based routing and priority-based escalation capabilities.
"""

import json
import logging
from typing import Any, Dict, Optional

import requests
from airflow.hooks.base import BaseHook
from airflow.models import Variable

from etdag_v2.notifications.base import BaseNotificationHandler
from etdag_v2.utils import is_production


class JSMOperationsNotificationHandler(BaseNotificationHandler):
    """Handler for JSM Operations notifications."""

    def __init__(
        self,
        priority: str = "P3",
        team: Optional[str] = None,
        environment: Optional[str] = None,
    ):
        """Initialize JSM Operations notification handler.

        Args:
            priority: Priority level for the alert (P1=Critical, P2=High, P3=Medium, P4=Low, P5=Lowest)
            team: Team name for routing alerts (uses team_owner from notification config)
            environment: Target environment
        """
        super().__init__(environment)
        self.priority = priority
        self.team = team

    def send_success_notification(self, context: Dict[str, Any]) -> Optional[Any]:
        """JSM Operations typically doesn't send success notifications.

        Args:
            context: Airflow context dictionary

        Returns:
            None (success notifications not typically sent to JSM Operations)
        """
        self.logger.info("JSM Operations success notifications are typically disabled")
        return None

    def send_failure_notification(self, context: Dict[str, Any]) -> Optional[Any]:
        """Send JSM Operations failure alert.

        Args:
            context: Airflow context dictionary

        Returns:
            JSM Operations API response or None if failed/disabled
        """
        if not is_production():
            self.logger.info(
                "JSM Operations alerts disabled in non-production environments"
            )
            return None

        return self._send_jsm_operations_alert(context)

    def _send_jsm_operations_alert(self, context: Dict[str, Any]) -> Optional[Any]:
        """Send alert to JSM Operations for production failures.

        Args:
            context: Airflow context dictionary

        Returns:
            JSM Operations API response or None if failed
        """
        try:
            context_info = self._extract_context_info(context)

            # Log the failure details
            self.logger.error(
                f"{context_info['dag_id']} has errored at {context_info['execution_date']}"
            )
            self.logger.error(f"Errored Task: {context_info['task_id']}")
            self.logger.error(
                f"Error: {context_info.get('exception') or context_info.get('reason')}"
            )

            # Get JSM Operations configuration from Airflow connection
            jsm_conn = self._get_jsm_connection()
            if not jsm_conn:
                return None

            # Build alert payload
            alert_payload = self._build_alert_payload(context_info, jsm_conn)

            # Send to JSM Operations API
            return self._send_to_jsm_api(alert_payload, jsm_conn)

        except (KeyError, ValueError, TypeError) as e:
            self._handle_notification_error(e, "jsm_operations")
            return None

    def _get_jsm_connection(self) -> Optional[Dict[str, Any]]:
        """Get JSM Operations connection configuration.

        Returns:
            Dictionary with JSM connection details or None if failed
        """
        try:
            jsm_conn = BaseHook.get_connection("jsm_operations_conn")

            cloud_id = jsm_conn.extra_dejson.get("cloud_id")
            team_id = jsm_conn.extra_dejson.get("team_id")
            api_token = jsm_conn.password
            email = jsm_conn.login

            if not all([cloud_id, team_id, api_token, email]):
                raise ValueError(
                    "Missing required JSM Operations connection parameters"
                )

            return {
                "cloud_id": cloud_id,
                "team_id": team_id,
                "api_token": api_token,
                "email": email,
            }

        except Exception as e:
            self.logger.error(f"Failed to get JSM Operations connection: {str(e)}")
            return None

    def _build_alert_payload(
        self, context_info: Dict[str, Any], jsm_conn: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Build JSM Operations alert payload.

        Args:
            context_info: Extracted context information
            jsm_conn: JSM connection configuration

        Returns:
            JSM Operations alert payload dictionary
        """
        error_msg = str(
            context_info.get("exception") or context_info.get("reason", "Unknown error")
        )

        # Resolve team ID for JSM Operations routing
        team_id = self._resolve_team_id(jsm_conn)

        return {
            "message": f"🚨 Airflow DAG Failure: {context_info['dag_id']}",
            "description": (
                f"DAG: {context_info['dag_id']}\n"
                f"Task: {context_info['task_id']}\n"
                f"Execution Date: {context_info['execution_date']}\n"
                f"Error: {error_msg}\n\n"
                f"DAG URL: https://airflow.k8s.eltoro.com/dags/{context_info['dag_id']}/grid"
            ),
            "responders": [{"id": team_id, "type": "team"}],
            "source": "Airflow",
            "entity": context_info["dag_id"],
            "alias": f"airflow-{context_info['dag_id']}-{context_info['execution_date']}",
            "tags": ["airflow", "critical", "dag-failure"],
            "priority": self.priority,
            "extraProperties": {
                "dag_id": context_info["dag_id"],
                "task_id": context_info["task_id"],
                "execution_date": str(context_info["execution_date"]),
                "environment": Variable.get("environment", default_var="unknown"),
            },
        }

    def _resolve_team_id(self, jsm_conn: Dict[str, Any]) -> str:
        """Resolve the JSM Operations team ID based on team configuration.

        Args:
            jsm_conn: JSM connection configuration

        Returns:
            Team ID for JSM Operations routing
        """
        # If no team specified, use default team from connection
        if not self.team:
            return jsm_conn["team_id"]

        # For now, we'll use a simple mapping approach
        # This can be extended to support multiple team configurations
        team_mapping = {
            "analytics-engineering": jsm_conn["team_id"],  # Default team
            "data-engineering": jsm_conn["team_id"],  # Route to same team for now
            "business-intelligence": jsm_conn["team_id"],  # Route to same team for now
            "platform-team": jsm_conn["team_id"],  # Route to same team for now
        }

        # Return mapped team ID or default to connection team ID
        return team_mapping.get(self.team, jsm_conn["team_id"])

    def _send_to_jsm_api(
        self, payload: Dict[str, Any], jsm_conn: Dict[str, Any]
    ) -> Optional[Any]:
        """Send alert to JSM Operations API.

        Args:
            payload: JSM Operations alert payload
            jsm_conn: JSM connection configuration

        Returns:
            API response or None
        """
        # JSM Operations API v1 (correct endpoint per documentation)
        url = f"https://api.atlassian.com/jsm/ops/api/{jsm_conn['cloud_id']}/v1/alerts"
        headers = {"Content-Type": "application/json", "Accept": "application/json"}

        # Use Basic Auth with email + API token (JSM Operations API requirement)
        auth = (jsm_conn["email"], jsm_conn["api_token"])

        try:
            self.logger.info(f"Sending JSM Operations alert to: {url}")
            self.logger.info(f"Alert payload: {json.dumps(payload, indent=2)}")

            response = requests.post(
                url, json=payload, headers=headers, auth=auth, timeout=30
            )

            self.logger.info(f"JSM API Response Status: {response.status_code}")
            self.logger.info(f"JSM API Response: {response.text}")

            response.raise_for_status()

            api_response = response.json()
            self.logger.info(
                f"JSM Operations alert created successfully: {api_response.get('requestId')}"
            )
            return api_response

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to send JSM Operations alert: {str(e)}")
            if hasattr(e, "response") and e.response is not None:
                self.logger.error(f"Response status: {e.response.status_code}")
                self.logger.error(f"Response body: {e.response.text}")
            return None
        except Exception as e:
            self.logger.error(
                f"Unexpected error sending JSM Operations alert: {str(e)}"
            )
            return None


def send_jsm_operations_alert(
    context: Dict[str, Any], priority: str = "P3", team: Optional[str] = None
) -> Optional[Any]:
    """Send JSM Operations alert for DAG failure.

    Args:
        context: Airflow context dictionary
        priority: Priority level for the alert (P1=Critical, P2=High, P3=Medium, P4=Low, P5=Lowest)
        team: Team name for routing alerts (optional)

    Returns:
        JSM Operations API response or None
    """
    handler = JSMOperationsNotificationHandler(priority=priority, team=team)
    return handler.send_failure_notification(context)
