"""
Core ETDAG class implementation.

This module contains the main ETDAG class that extends Airflow's DAG
with enhanced validation, safety features, and monitoring capabilities.
"""

import logging
import os
import sys
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, List, Optional, Union

import pendulum
from airflow.models.dag import DAG


def _setup_config_imports():
    """Setup config imports with simple fallback for different deployment scenarios."""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)

    # Ensure parent directory is in Python path
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

    # Also ensure common Airflow paths are available
    airflow_paths = ["/opt/airflow/dags/repo", "/opt/airflow/dags"]
    for path in airflow_paths:
        if os.path.exists(path) and path not in sys.path:
            sys.path.insert(0, path)

    try:
        # Direct imports - this should work based on production testing
        from et_config.environments.registry import (
            get_current_environment as _get_current_environment,
        )
        from et_config.environments.registry import (
            get_environment_config as _get_environment_config,
        )
        from et_config.models.notifications import NotificationConfig
        from et_config.teams.utils import get_team_emails

        return (
            NotificationConfig,
            _get_current_environment,
            _get_environment_config,
            get_team_emails,
        )
    except ImportError as e:
        # If direct import fails, provide debug info and raise
        print(f"ERROR: Config imports failed with: {e}")
        print(f"Current directory: {os.getcwd()}")
        print(f"Script location: {current_dir}")
        print(f"Python path: {sys.path[:10]}")

        # Check for config directories
        config_dirs = []
        for path in sys.path:
            config_path = os.path.join(path, "config")
            if os.path.exists(config_path):
                config_dirs.append(config_path)
        print(f"Found config directories: {config_dirs}")

        # Re-raise the original error with additional context
        raise ImportError(
            f"Failed to import config modules: {e}. Debug info logged above."
        ) from e


# Setup imports
NotificationConfig, get_current_environment, get_environment_config, get_team_emails = (
    _setup_config_imports()
)

# Import other modules after config is set up
from etdag_v2.notifications.jsm import JSMOperationsNotificationHandler
from etdag_v2.notifications.slack import SlackNotificationHandler
from etdag_v2.utils import log_failure, log_success
from etdag_v2.validation import (
    apply_production_safety_checks,
    enhance_tags,
    validate_required_fields,
)


class ETDAG(DAG):
    """Enhanced ElToro DAG class with validation, safety features, and monitoring.

    This class extends the standard Airflow DAG with:
    - Required field validation (owner, description, tags)
    - Default retry configuration
    - Environment-aware alerting
    - Safety checks for production environments
    - Enhanced metadata support
    - Modular notification system

    Args:
        dag_id (str): The id of the DAG; must consist exclusively of alphanumeric characters, dashes, dots and underscores
        owner (str): Required. The owner of the DAG (team or individual)
        description (str): Required. Description of what this DAG does
        tags (List[str]): Required. List of tags for categorization and filtering
        business_purpose (str, optional): Required for production. Business justification for this DAG
        data_sources (List[str], optional): List of data sources consumed by this DAG
        downstream_systems (List[str], optional): List of systems that depend on this DAG's output
        start_date (datetime, optional): Start date for the DAG. Defaults to yesterday
        schedule (Union[timedelta, str], optional): How often to run. Defaults to @daily
        max_active_tasks (int, optional): Max concurrent tasks. Defaults to 1
        max_active_runs (int, optional): Max concurrent DAG runs. Defaults to 1
        default_retries (int, optional): Default number of retries for tasks. Defaults to 3
        retry_delay (timedelta, optional): Delay between retries. Defaults to 5 minutes
        sla_minutes (int, optional): SLA in minutes for the DAG
        dagrun_timeout (timedelta, optional): Timeout for DAG runs
        notification_config (NotificationConfig, optional): Notification settings
        alert_routing (Dict[str, str], optional): Route alerts based on criticality
        **kwargs: Additional arguments passed to base DAG class

    Raises:
        DAGValidationError: If required fields are missing or invalid
    """

    # Class-level configuration
    REQUIRED_FIELDS = ["owner", "description", "tags"]
    REQUIRED_PROD_FIELDS = ["business_purpose"]
    VALID_ENVIRONMENTS = ["local", "dev", "staging", "prod"]
    DEFAULT_RETRY_DELAY = timedelta(minutes=5)
    DEFAULT_SLA_MINUTES = 1440  # 24 hours

    def __init__(
        self,
        dag_id: str,
        dag_owner: str,
        description: str,
        tags: List[str],
        business_purpose: Optional[str] = None,
        data_sources: Optional[List[str]] = None,
        downstream_systems: Optional[List[str]] = None,
        start_date=pendulum.yesterday(),
        schedule: Optional[Union[timedelta, str]] = "@daily",
        max_active_tasks: int = 1,
        max_active_runs: int = 1,
        default_retries: int = 3,
        retry_delay: Optional[timedelta] = None,
        sla_minutes: Optional[int] = None,
        dagrun_timeout: Optional[timedelta] = None,
        notification_config: Optional[Any] = None,
        alert_routing: Optional[Dict[str, str]] = None,
        # Legacy compatibility parameters
        et_success_msg: bool = False,
        et_failure_msg: bool = True,
        extra_slack_conn_ids_to_message: Optional[List[str]] = None,
        **kwargs,
    ) -> None:
        """Initialize the ETDAG with validation and enhanced features."""

        # Store instance attributes for access
        self.dag_owner = dag_owner
        self._original_tags = list(tags) if tags else []
        self.business_purpose = business_purpose
        self.data_sources = data_sources or []
        self.downstream_systems = downstream_systems or []
        self.sla_minutes = sla_minutes
        self.alert_routing = alert_routing or {}
        self.environment = get_current_environment()
        self.env_config = get_environment_config()

        # Handle notification configuration
        if notification_config is None:
            notification_config = NotificationConfig(
                email_on_success=False,
                email_on_failure=False,
                slack_on_success=et_success_msg,
                slack_on_failure=et_failure_msg,
                team_owner=dag_owner,  # Use the DAG owner as the team owner for notifications
            )

        self.notification_config = notification_config
        self.extra_slack_conn_ids_to_message = extra_slack_conn_ids_to_message or []

        # Validate required fields
        validate_required_fields(
            dag_id=dag_id,
            owner=dag_owner,
            description=description,
            tags=tags,
            business_purpose=business_purpose,
        )

        # Set defaults
        if retry_delay is None:
            retry_delay = self.DEFAULT_RETRY_DELAY

        # Configure callbacks
        success_callback = self._get_success_callback()
        failure_callback = self._get_failure_callback()

        # Prepare default_args with retry configuration
        default_args = kwargs.get("default_args", {})
        default_args.update(
            {
                "dag_owner": dag_owner,
                "retries": default_retries,
                "retry_delay": retry_delay,
                "email_on_failure": (
                    notification_config.email_on_failure
                    if notification_config
                    else False
                ),
                "email_on_retry": False,  # Generally don't email on retries
            }
        )

        # Add email configuration if needed
        if notification_config and (
            notification_config.email_on_success or notification_config.email_on_failure
        ):
            # Use email destinations from notification config or fall back to owner-based list
            default_args["email"] = (
                notification_config.email_destination_override
                or get_team_emails(dag_owner)
            )

        # Update kwargs with all our settings
        kwargs.update(
            {
                "dag_id": dag_id,
                "description": description,
                "tags": enhance_tags(
                    tags, dag_owner, self.environment, business_purpose
                ),
                "start_date": start_date,
                "schedule": schedule,
                "max_active_tasks": max_active_tasks,
                "max_active_runs": max_active_runs,
                "dagrun_timeout": dagrun_timeout,
                "on_success_callback": success_callback,
                "on_failure_callback": failure_callback,
                "default_args": default_args,
            }
        )

        # Add production safety checks
        if self.env_config.is_prod:
            apply_production_safety_checks(kwargs)

        # Initialize parent DAG class
        super().__init__(**kwargs)

        # Log DAG creation
        self._log_dag_creation()

    @property
    def owner(self) -> str:
        """Get the owner of the DAG."""
        return self.dag_owner

    def get_s3_path(self, path_type: str, suffix: str = "") -> str:
        """Get environment-appropriate S3 path.

        Args:
            path_type: Type of path (e.g., 'staging', 'processed', 'exports')
            suffix: Additional path suffix

        Returns:
            str: Full S3 path for the current environment
        """
        return self.env_config.s3.get_path(self.dag_id, path_type, suffix)

    def get_starburst_connection(self) -> str:
        """Get the appropriate Starburst connection for current environment."""
        return self.env_config.connections.starburst_conn_id

    def _get_success_callback(self):
        """Get the appropriate success callback function."""
        if self.notification_config and self.notification_config.slack_on_success:
            return self._create_slack_success_callback()
        elif self.notification_config and self.notification_config.email_on_success:
            return log_success
        else:
            return None

    def _get_failure_callback(self):
        """Get the appropriate failure callback function."""
        if self.notification_config and self.notification_config.slack_on_failure:
            return self._create_slack_failure_callback()
        elif self.notification_config and self.notification_config.email_on_failure:
            return log_failure
        else:
            return None

    def _create_slack_success_callback(self):
        """Create a Slack success callback with current configuration."""

        def slack_success_callback(context):
            handler = SlackNotificationHandler(
                channel=(
                    self.notification_config.slack_channel_override
                    if self.notification_config
                    else None
                ),
                environment=self.environment,
            )
            return handler.send_success_notification(context)

        return slack_success_callback

    def _create_slack_failure_callback(self):
        """Create a Slack failure callback with current configuration."""

        def slack_failure_callback(context):
            # Send Slack notification
            slack_handler = SlackNotificationHandler(
                channel=(
                    self.notification_config.slack_channel_override
                    if self.notification_config
                    else None
                ),
                environment=self.environment,
            )
            slack_result = slack_handler.send_failure_notification(context)

            # Send JSM Operations notification if enabled and in production
            if (
                self.env_config.is_prod
                and self.notification_config
                and self.notification_config.jsm_operations_alert
            ):
                jsm_handler = JSMOperationsNotificationHandler(
                    priority=self.notification_config.jsm_operations_priority,
                    team=self.notification_config.team_owner,
                    environment=self.environment,
                )
                jsm_handler.send_failure_notification(context)

            return slack_result

        return slack_failure_callback

    def _log_dag_creation(self) -> None:
        """Log DAG creation with enhanced metadata."""
        logging.info(
            "Created ETDAG '%s' - Owner: %s, Environment: %s, Tags: %s, Business Purpose: %s",
            self.dag_id,
            self.dag_owner,
            self.environment,
            self._original_tags,
            self.business_purpose or "Not specified",
        )
